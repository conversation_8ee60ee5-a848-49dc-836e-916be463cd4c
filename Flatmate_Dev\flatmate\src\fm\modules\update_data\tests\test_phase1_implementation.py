"""
Test suite for Phase 1 implementation - File Info Enrichment.

This test validates:
1. FileInfoData model creation and properties
2. FileInfoManager enrichment functionality
3. Event structure with enriched data
4. UI component compatibility
"""

import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime
from pathlib import Path

# Import the new models and components
from fm.modules.update_data.models.file_info import FileInfoData
from fm.modules.update_data.models.config import SourceOptions, SaveOptions
from fm.modules.update_data._ui.ui_events import FileListUpdatedEvent
from fm.modules.update_data._ui._presenter.file_info_manager import FileInfoManager


class TestFileInfoData(unittest.TestCase):
    """Test the unified FileInfoData model."""

    def setUp(self):
        """Set up test data."""
        self.sample_service_data = {
            'path': '/test/sample.csv',
            'bank_type': 'Kiwibank',
            'format_type': 'Basic CSV',
            'handler': 'KiwibankCSVHandler',
            'size_bytes': 1024,
            'size_str': '1.0 KB'
        }

    def test_file_info_data_creation(self):
        """Test FileInfoData creation from service data."""
        file_info = FileInfoData.from_service_data(self.sample_service_data)
        
        self.assertEqual(file_info.path, '/test/sample.csv')
        self.assertEqual(file_info.bank_type, 'Kiwibank')
        self.assertEqual(file_info.format_type, 'Basic CSV')
        self.assertEqual(file_info.handler, 'KiwibankCSVHandler')
        self.assertEqual(file_info.size_bytes, 1024)
        self.assertEqual(file_info.size_str, '1.0 KB')

    def test_file_info_properties(self):
        """Test FileInfoData computed properties."""
        file_info = FileInfoData.from_service_data(self.sample_service_data)
        
        # Test name property
        self.assertEqual(file_info.name, 'sample.csv')
        
        # Test size_formatted property
        self.assertEqual(file_info.size_formatted, '1.0 KB')
        
        # Test file_info_display property
        self.assertEqual(file_info.file_info_display, 'Kiwibank | Basic CSV')

    def test_file_info_display_edge_cases(self):
        """Test file_info_display for edge cases."""
        # Unknown bank and format
        unknown_data = {
            'path': '/test/unknown.txt',
            'bank_type': 'Unknown',
            'format_type': 'Unrecognized',
            'handler': None,
            'size_bytes': 512,
            'size_str': '512 B'
        }
        file_info = FileInfoData.from_service_data(unknown_data)
        self.assertEqual(file_info.file_info_display, 'TXT File')
        
        # Known bank, unknown format
        partial_data = {
            'path': '/test/partial.csv',
            'bank_type': 'Kiwibank',
            'format_type': 'Unrecognized',
            'handler': None,
            'size_bytes': 256,
            'size_str': '256 B'
        }
        file_info = FileInfoData.from_service_data(partial_data)
        self.assertEqual(file_info.file_info_display, 'Kiwibank')

    def test_to_dict_method(self):
        """Test FileInfoData to_dict conversion."""
        file_info = FileInfoData.from_service_data(self.sample_service_data)
        result_dict = file_info.to_dict()
        
        # Check all expected keys are present
        expected_keys = [
            'path', 'bank_type', 'format_type', 'handler', 'size_bytes', 'size_str',
            'modified', 'created', 'is_valid', 'is_processed', 'name', 
            'size_formatted', 'file_info_display', 'created_formatted'
        ]
        for key in expected_keys:
            self.assertIn(key, result_dict)


class TestFileInfoManagerEnrichment(unittest.TestCase):
    """Test FileInfoManager enrichment functionality."""

    def setUp(self):
        """Set up test environment."""
        self.mock_folder_monitor = MagicMock()
        self.mock_local_bus = MagicMock()
        self.mock_file_info_service = MagicMock()
        
        # Mock file info service response
        self.mock_file_info_service.discover_files.return_value = [
            {
                'path': '/test/file1.csv',
                'bank_type': 'Kiwibank',
                'format_type': 'Basic CSV',
                'handler': 'KiwibankCSVHandler',
                'size_bytes': 1024,
                'size_str': '1.0 KB'
            },
            {
                'path': '/test/file2.csv',
                'bank_type': 'ASB',
                'format_type': 'Standard CSV',
                'handler': 'ASBCSVHandler',
                'size_bytes': 2048,
                'size_str': '2.0 KB'
            }
        ]
        
        self.file_info_manager = FileInfoManager(
            folder_monitor_service=self.mock_folder_monitor,
            local_bus=self.mock_local_bus,
            file_info_service=self.mock_file_info_service
        )

    def test_set_files_enrichment(self):
        """Test that set_files enriches data using FileInfoService."""
        test_files = ['/test/file1.csv', '/test/file2.csv']
        
        self.file_info_manager.set_files(test_files, '/test')

        # Verify FileInfoService was called
        self.mock_file_info_service.discover_files.assert_called_once_with(test_files)

        # Verify enriched data is stored
        self.assertEqual(len(self.file_info_manager.file_info_list), 2)
        self.assertIsInstance(self.file_info_manager.file_info_list[0], FileInfoData)
        self.assertEqual(self.file_info_manager.file_info_list[0].bank_type, 'Kiwibank')
        self.assertEqual(self.file_info_manager.file_info_list[1].bank_type, 'ASB')

    def test_add_files_enrichment(self):
        """Test that add_files enriches new files."""
        # Set initial files
        initial_files = ['/test/file1.csv']
        self.file_info_manager.set_files(initial_files, '/test')

        # Reset mock to track new calls
        self.mock_file_info_service.reset_mock()
        self.mock_file_info_service.discover_files.return_value = [
            {
                'path': '/test/file3.csv',
                'bank_type': 'BNZ',
                'format_type': 'Extended CSV',
                'handler': 'BNZCSVHandler',
                'size_bytes': 3072,
                'size_str': '3.0 KB'
            }
        ]

        # Add new files
        new_files = ['/test/file3.csv']
        result = self.file_info_manager.add_files(new_files)

        self.assertTrue(result)
        self.mock_file_info_service.discover_files.assert_called_once_with(new_files)
        self.assertEqual(len(self.file_info_manager.file_info_list), 2)  # 1 initial + 1 new

    def test_event_emission_with_enriched_data(self):
        """Test that events are emitted with enriched FileInfoData objects."""
        test_files = ['/test/file1.csv']

        self.file_info_manager.set_files(test_files, '/test')
        
        # Verify event was emitted
        self.mock_local_bus.emit.assert_called()
        
        # Get the emitted event data
        call_args = self.mock_local_bus.emit.call_args
        event_data = call_args[0][1]  # Second argument is the event data
        
        # Verify it's a FileListUpdatedEvent with enriched data
        self.assertIsInstance(event_data, FileListUpdatedEvent)
        self.assertEqual(len(event_data.files), 1)
        self.assertIsInstance(event_data.files[0], FileInfoData)
        self.assertEqual(event_data.files[0].bank_type, 'Kiwibank')


class TestConfigModels(unittest.TestCase):
    """Test the moved configuration models."""

    def test_source_options_import(self):
        """Test that SourceOptions can be imported from new location."""
        self.assertEqual(SourceOptions.SELECT_FOLDER, "Select entire folder...")
        self.assertEqual(SourceOptions.SELECT_FILES, "Select individual files...")

    def test_save_options_import(self):
        """Test that SaveOptions can be imported from new location."""
        self.assertEqual(SaveOptions.SAME_AS_SOURCE, "Same as Source Files")
        self.assertEqual(SaveOptions.SELECT_LOCATION, "Select save location...")


class TestEventStructure(unittest.TestCase):
    """Test the updated event structure."""

    def test_file_list_updated_event_with_enriched_data(self):
        """Test FileListUpdatedEvent accepts FileInfoData objects."""
        file_info = FileInfoData(
            path='/test/sample.csv',
            bank_type='Kiwibank',
            format_type='Basic CSV',
            handler='KiwibankCSVHandler',
            size_bytes=1024,
            size_str='1.0 KB'
        )
        
        event = FileListUpdatedEvent(
            files=[file_info],
            source_path='/test'
        )
        
        self.assertEqual(len(event.files), 1)
        self.assertIsInstance(event.files[0], FileInfoData)
        self.assertEqual(event.files[0].bank_type, 'Kiwibank')
        self.assertEqual(event.source_path, '/test')


if __name__ == '__main__':
    unittest.main()
