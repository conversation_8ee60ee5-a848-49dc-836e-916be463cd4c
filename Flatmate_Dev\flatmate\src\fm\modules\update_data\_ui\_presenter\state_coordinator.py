"""
State management for Update Data module.

This module contains the UpdateDataState dataclass and consolidated state management logic.
Consolidates StateManager and WidgetStateManager as part of the consolidation refactoring.
"""

from dataclasses import dataclass, field
from typing import List, TYPE_CHECKING

from fm.core.services.logger import log

if TYPE_CHECKING:
    from ..interface import IUpdateDataView


@dataclass
class UpdateDataState:
    """
    Presenter state for Update Data module.

    Following MVP pattern: Presenter owns all state, View is stateless.
    This replaces the archived view_context_manager approach.
    """
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"

    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""

    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0

    # Recent folders (Quick Access) - Phase 3
    recent_source_folders: List[str] = field(default_factory=list)

    def update_can_process(self) -> None:
        """Update can_process based on current state."""
        self.can_process = (
            self.source_configured and
            self.destination_configured and
            not self.processing and
            len(self.selected_files) > 0
        )

        # Update process button text - keep it simple
        if self.processing:
            self.process_button_text = "Processing..."
        else:
            self.process_button_text = "Process Files"

    def reset(self) -> None:
        """Reset state to initial values."""
        self.source_configured = False
        self.source_type = ""
        self.source_path = ""
        self.selected_files.clear()
        self.destination_configured = False
        self.processing = False
        self.error_message = ""
        self.status_message = "Select source files or folder to begin"
        self.update_can_process()


class StateManager:
    """
    Consolidated state and UI synchronization management.

    This class combines:
    - State data management (UpdateDataState)
    - UI synchronization (from WidgetStateManager)
    - State-to-view updates
    - Guide pane management
    """

    def __init__(self, view: 'IUpdateDataView', info_bar_service, folder_monitor_service):
        """
        Initialize the consolidated state manager.

        Args:
            view: The view interface for UI updates
            info_bar_service: Service for info bar messages
            folder_monitor_service: Service for folder monitoring
        """
        # State data
        self.state = UpdateDataState()

        # UI sync dependencies
        self.view = view
        self.info_bar_service = info_bar_service
        self.folder_monitor_service = folder_monitor_service

    # =============================================================================
    # STATE MANAGEMENT METHODS
    # =============================================================================

    def update_can_process(self):
        """Delegate to state object."""
        self.state.update_can_process()

    def reset(self):
        """Delegate to state object."""
        self.state.reset()

    # =============================================================================
    # UI SYNCHRONIZATION METHODS (from WidgetStateManager)
    # =============================================================================

    def sync_state_to_view(self) -> None:
        """
        Sync presenter state to view.

        MVP pattern: Presenter state is source of truth, view reflects state.
        This replaces the archived view_context_manager approach.
        """
        try:
            # Update process button state and text
            self.view.set_process_enabled(self.state.can_process)
            self.view.set_process_button_text(self.state.process_button_text)

            # Update status message
            if self.state.status_message:
                self.info_bar_service.publish_message(self.state.status_message)

            # Update guide pane based on current state
            self.update_guide_pane()

        except Exception as e:
            # Log error but don't crash the application
            log.error(f"Error syncing state to view: {e}")

    def update_guide_pane(self) -> None:
        """Update guide pane content based on current state."""
        try:
            if self.state.source_type == 'folder':
                self.update_guide_pane_for_folder()
            elif self.state.source_type == 'files':
                self.update_guide_pane_for_files()
            else:
                # Default guide content - use center panel's guide pane
                if hasattr(self.view, 'center_display') and self.view.center_display:
                    self.view.center_display.guide_pane.set_state('initial')

        except Exception as e:
            log.error(f"Error updating guide pane: {e}")

    def update_guide_pane_for_folder(self) -> None:
        """Update guide pane for folder source type."""
        try:
            # Use the center panel's guide pane through proper channels
            if hasattr(self.view, 'center_display') and self.view.center_display:
                folder_path = getattr(self.state, 'selected_folder', self.state.source_path)
                file_count = len(getattr(self.state, 'selected_files', []))

                # Check folder monitoring status using correct method
                is_monitoring = self.folder_monitor_service.is_folder_monitored(folder_path) if folder_path else False

                # Use guide pane's set_state method with proper context
                self.view.center_display.guide_pane.set_state('folder_selected', {
                    'path': folder_path,
                    'count': file_count
                })

                # Show folder monitoring option
                self.view.center_display.guide_pane.show_folder_monitoring_option(enabled=is_monitoring)

        except Exception as e:
            log.error(f"Error updating folder guide: {e}")

    def update_guide_pane_for_files(self) -> None:
        """Update guide pane for files source type."""
        try:
            # Use the center panel's guide pane through proper channels
            if hasattr(self.view, 'center_display') and self.view.center_display:
                file_count = len(self.state.selected_files)

                # Use guide pane's set_state method with proper context
                self.view.center_display.guide_pane.set_state('files_selected', {
                    'count': file_count
                })

        except Exception as e:
            log.error(f"Error updating files guide: {e}")
