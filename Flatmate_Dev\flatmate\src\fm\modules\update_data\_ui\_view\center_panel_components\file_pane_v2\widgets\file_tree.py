"""
File tree widget for displaying file information in a folder/file hierarchy.

Refactored from table to tree to support:
- Tree-like folder/file display with icons
- Default visible columns: Type, Created
- Optional columns: Name, Size, Path (toggleable via header context menu)
- Resizable columns with sensible defaults; long names elided
"""

from PySide6.QtWidgets import (
    QTreeWidget, QTreeWidgetItem, QHeaderView, QAbstractItemView, QMenu
)
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QIcon
from typing import List, Optional
from ..models import FileInfo, FileViewModel
from ..config import FileConfig
from ..utils import format_file_size, group_files_by_directory
from fm.core.services.logger import log
import os
from pathlib import Path


class FileTree(QTreeWidget):
    """Tree widget for displaying files grouped by directory."""

    # Signals
    file_selected = Signal(str)       # file_path
    file_double_clicked = Signal(str) # file_path

    def __init__(self, model: FileViewModel, config: FileConfig, parent=None):
        super().__init__(parent)
        self._model = model
        self._config = config
        self._columns: List[str] = []
        self._folder_items: dict[str, QTreeWidgetItem] = {}
        self._focused_mode = True  # TEMPORARY: Start in focused mode for testing
        # Ensure this widget picks up QSS theme styles (theme.qss uses #file_tree selectors)
        self.setObjectName("file_tree")
        # Optional: enable alternating row colors so future QSS can style ::item:alternate
        self.setAlternatingRowColors(True)
        self._setup_tree()
        self._connect_signals()

    def _setup_tree(self) -> None:
        """Initialize tree widget and columns."""
        # Default utilitarian columns: Name, Size, File Info, Created
        self._columns = ["Name", "Size", "File Info", "Created"]

        # Optional columns (hidden by default, can be toggled via context menu)
        if self._config.show_file_type:
            self._columns.append("Type")  # File extension type
        # Path column is optional and hidden by default (redundant with tree structure)
        # Users can enable it via context menu if needed

        self.setColumnCount(len(self._columns))
        self.setHeaderLabels(self._columns)

        # Selection behavior
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Header behavior and sizing
        header = self.header()
        header.setSectionsClickable(True)
        header.setHighlightSections(False)
        header.setStretchLastSection(False)

        # Name column resizable and stretch with eliding
        name_idx = self._columns.index("Name")
        header.setSectionResizeMode(name_idx, QHeaderView.ResizeMode.Interactive)
        self.setColumnWidth(name_idx, 200)

        # Size column fit to contents
        size_idx = self._columns.index("Size")
        header.setSectionResizeMode(size_idx, QHeaderView.ResizeMode.ResizeToContents)

        # File Info column (shows statement handler info) - wider for bank names
        file_info_idx = self._columns.index("File Info")
        header.setSectionResizeMode(file_info_idx, QHeaderView.ResizeMode.Interactive)
        self.setColumnWidth(file_info_idx, 180)

        # Created column fit to contents
        created_idx = self._columns.index("Created")
        header.setSectionResizeMode(created_idx, QHeaderView.ResizeMode.ResizeToContents)

        # Optional Type column fit to contents if present
        if "Type" in self._columns:
            idx = self._columns.index("Type")
            header.setSectionResizeMode(idx, QHeaderView.ResizeMode.ResizeToContents)

        # Context menu to toggle optional columns
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_header_context_menu)

    def _connect_signals(self) -> None:
        """Connect internal signals."""
        self.itemSelectionChanged.connect(self._on_selection_changed)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)

    def refresh_data(self) -> None:
        """Rebuild tree from model files."""
        try:
            # Branch on focused mode
            if self._focused_mode:
                self._refresh_data_focused()
            else:
                # Original full hierarchy mode
                self.clear()
                self._folder_items.clear()

                files = self._model.files
                # Group by parent directory
                groups = group_files_by_directory(files)

                for folder_path, file_infos in groups.items():
                    folder_item = self._ensure_folder_item(folder_path)
                    for fi in file_infos:
                        self._add_file_item(folder_item, fi)

                self.expandAll()

                # Restore selection if possible
                if self._model.selected_file:
                    self._select_file_by_path(self._model.selected_file)

            # Apply column width policy after tree is built
            self._apply_column_width_policy()

        except Exception as e:
            log.error(f"Error refreshing file tree data: {e}")

    def set_focused_mode(self, enabled: bool) -> None:
        """Set focused mode to show only file, parent, and grandparent levels.

        Args:
            enabled: If True, shows only 3 levels (file, parent, grandparent).
                    If False, shows full directory hierarchy.
        """
        if self._focused_mode != enabled:
            self._focused_mode = enabled
            self.refresh_data()  # Rebuild tree with new mode

    def _refresh_data_focused(self, depth: int = 2) -> None:
        """Rebuild tree in focused mode showing only limited hierarchy.

        Args:
            depth: Number of parent levels to show (default 2 = file, parent, grandparent)
        """
        try:
            self.clear()
            self._folder_items.clear()

            files = self._model.files
            if not files:
                return

            def get_parent_chain(path: str, depth: int = 2) -> list[str]:
                """Get parent chain for a file path.

                Returns:
                    List of [file_path, parent_dir, grandparent_dir, ...]
                """
                from pathlib import Path
                p = Path(path)
                chain = [str(p)]
                for _ in range(depth):
                    p = p.parent
                    if p == p.parent:  # Reached root
                        break
                    chain.append(str(p))
                return chain

            # Build minimal tree across all files
            for fi in files:
                chain = get_parent_chain(fi.path, depth)
                if len(chain) < 2:
                    # File at root level, create a simple parent
                    parent_item = self._ensure_specific_folder_item(str(Path(fi.path).parent), allow_recurse=False)
                    self._add_file_item(parent_item, fi)
                else:
                    # Normal case: file, parent, grandparent
                    file_path = chain[0]
                    parent_dir = chain[1]
                    grandparent_dir = chain[2] if len(chain) > 2 else parent_dir

                    # Ensure grandparent node (top-level)
                    gp_item = self._ensure_specific_folder_item(grandparent_dir, allow_recurse=False)

                    # Ensure parent under grandparent (only if different)
                    if parent_dir != grandparent_dir:
                        parent_item = self._ensure_specific_folder_item(parent_dir, parent_override=gp_item, allow_recurse=False)
                    else:
                        parent_item = gp_item

                    # Add file under parent
                    self._add_file_item(parent_item, fi)

            self.expandAll()

            # Restore selection if possible
            if self._model.selected_file:
                self._select_file_by_path(self._model.selected_file)

            # Apply column width policy after tree is built
            self._apply_column_width_policy()

        except Exception as e:
            print(f"Error refreshing focused file tree: {e}")

    def _ensure_specific_folder_item(self, folder_path: str, parent_override: Optional[QTreeWidgetItem] = None, allow_recurse: bool = False) -> QTreeWidgetItem:
        """Create or get folder item with controlled recursion.

        Args:
            folder_path: Path to the folder
            parent_override: Specific parent item to use (overrides recursion)
            allow_recurse: Whether to allow recursive parent creation

        Returns:
            QTreeWidgetItem for the folder
        """
        if folder_path in self._folder_items:
            return self._folder_items[folder_path]

        # Optionally block recursion unless explicitly requested
        parent_item = parent_override
        if parent_item is None and allow_recurse:
            parent_dir = str(Path(folder_path).parent)
            if parent_dir and parent_dir != folder_path:
                parent_item = self._ensure_specific_folder_item(parent_dir, None, allow_recurse=True)

        item = QTreeWidgetItem()
        item.setText(self._columns.index("Name"), self._get_display_folder_name(folder_path))
        item.setData(self._columns.index("Name"), Qt.ItemDataRole.UserRole, None)  # no file path on folder

        # Set folder icon to distinguish from files
        try:
            folder_icon = self.style().standardIcon(self.style().StandardPixmap.SP_DirIcon)
            item.setIcon(self._columns.index("Name"), folder_icon)
        except Exception:
            pass  # Graceful fallback if icon not available

        if parent_item:
            parent_item.addChild(item)
        else:
            self.addTopLevelItem(item)

        self._folder_items[folder_path] = item
        return item

    def _apply_column_width_policy(self) -> None:
        """Apply column width policy based on current mode."""
        header = self.header()
        header.setMinimumSectionSize(60)

        name_idx = self._columns.index("Name")
        size_idx = self._columns.index("Size")
        info_idx = self._columns.index("File Info")
        created_idx = self._columns.index("Created")

        # Keep crisp autosizing for size and created
        header.setSectionResizeMode(size_idx, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(created_idx, QHeaderView.ResizeMode.ResizeToContents)

        # Set Name column to stretch to fill available space (this fixes the user's issue!)
        header.setSectionResizeMode(name_idx, QHeaderView.ResizeMode.Stretch)

        # File Info column uses interactive mode with appropriate width
        header.setSectionResizeMode(info_idx, QHeaderView.ResizeMode.Interactive)

        if self._focused_mode:
            # Focused: wider info column since we have more space
            self.setColumnWidth(info_idx, 240)
        else:
            # Default: standard info column width
            self.setColumnWidth(info_idx, 180)

        # Handle optional Type column if present
        if "Type" in self._columns:
            type_idx = self._columns.index("Type")
            header.setSectionResizeMode(type_idx, QHeaderView.ResizeMode.ResizeToContents)

    def _ensure_folder_item(self, folder_path: str) -> QTreeWidgetItem:
        """Ensure a folder node exists."""
        if folder_path in self._folder_items:
            return self._folder_items[folder_path]

        # Build parent folders recursively
        parent_dir = str(Path(folder_path).parent)
        parent_item = None
        if parent_dir and parent_dir != folder_path:
            if parent_dir in self._folder_items:
                parent_item = self._folder_items[parent_dir]
            else:
                parent_item = self._ensure_folder_item(parent_dir)

        # Create user-friendly folder display name
        folder_name = self._get_display_folder_name(folder_path)
        item = QTreeWidgetItem()
        item.setText(self._columns.index("Name"), folder_name)
        item.setData(self._columns.index("Name"), Qt.ItemDataRole.UserRole, None)  # no file path on folder

        # Set folder icon to distinguish from files
        try:
            # Use system folder icon if available
            folder_icon = self.style().standardIcon(self.style().StandardPixmap.SP_DirIcon)
            item.setIcon(self._columns.index("Name"), folder_icon)
        except Exception:
            pass  # Graceful fallback if icon not available

        if parent_item:
            parent_item.addChild(item)
        else:
            self.addTopLevelItem(item)

        self._folder_items[folder_path] = item
        return item

    def _get_display_folder_name(self, folder_path: str) -> str:
        """Get user-friendly folder display name.

        Implements user preference for showing path relative to home directory (~)
        when possible, otherwise shows the full folder name for context.
        """
        try:
            path_obj = Path(folder_path)
            home_path = Path.home()

            # Try to show path relative to home directory
            try:
                relative_path = path_obj.relative_to(home_path)
                return f"~/{relative_path}"
            except ValueError:
                # If not under home directory, show a more contextual path
                # Show last 2 parts of the path for better context
                parts = path_obj.parts
                if len(parts) >= 2:
                    return f".../{parts[-2]}/{parts[-1]}"
                else:
                    return path_obj.name or str(path_obj)

        except Exception:
            # Fallback to simple folder name
            return Path(folder_path).name or folder_path

    def _add_file_item(self, parent_item: QTreeWidgetItem, fi: FileInfo) -> None:
        """Add a file row under given folder node."""
        it = QTreeWidgetItem(parent_item)
        # Name
        filename = Path(fi.path).name
        it.setText(self._columns.index("Name"), filename)
        it.setData(self._columns.index("Name"), Qt.ItemDataRole.UserRole, fi.path)

        # Set file icon to distinguish from folders
        try:
            # Use system file icon if available
            file_icon = self.style().standardIcon(self.style().StandardPixmap.SP_FileIcon)
            it.setIcon(self._columns.index("Name"), file_icon)
        except Exception:
            pass  # Graceful fallback if icon not available
        # Size - use size_str from FileInfoData
        it.setText(self._columns.index("Size"), fi.size_str or "Unknown")
        # File Info (shows enriched info like "Kiwibank | Basic CSV")
        it.setText(self._columns.index("File Info"), fi.file_info_display)
        # Created - use created from FileInfoData (format if datetime)
        created_text = fi.created.strftime("%d/%m/%Y %H:%M") if fi.created else "Unknown"
        it.setText(self._columns.index("Created"), created_text)
        # Optional Type column (file extension)
        if "Type" in self._columns:
            extension = Path(fi.path).suffix.lower() or "No extension"
            it.setText(self._columns.index("Type"), extension)

    def _select_file_by_path(self, file_path: str) -> None:
        """Select a file by its path."""
        def walk(item: QTreeWidgetItem):
            fp = item.data(self._columns.index("Name"), Qt.ItemDataRole.UserRole)
            if fp == file_path:
                self.setCurrentItem(item)
                return True
            for i in range(item.childCount()):
                if walk(item.child(i)):
                    return True
            return False

        for i in range(self.topLevelItemCount()):
            if walk(self.topLevelItem(i)):
                break

    def _on_selection_changed(self) -> None:
        """Handle selection change."""
        item = self.currentItem()
        if not item:
            self._model.set_selected_file(None)
            return
        file_path = item.data(self._columns.index("Name"), Qt.ItemDataRole.UserRole)
        if file_path:
            self._model.set_selected_file(file_path)
            self.file_selected.emit(file_path)
        else:
            self._model.set_selected_file(None)

    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int) -> None:
        """Handle item double click."""
        file_path = item.data(self._columns.index("Name"), Qt.ItemDataRole.UserRole)
        if file_path:
            self.file_double_clicked.emit(file_path)

    def _show_header_context_menu(self, pos: QPoint) -> None:
        """Context menu to toggle optional columns."""
        menu = QMenu(self)
        # Core columns are always visible: Name, Size, Status, Created
        # Optional columns can be toggled
        optional_columns = ["Type"]  # Path column removed - redundant with tree structure

        for col in optional_columns:
            if col in self._columns:
                idx = self._columns.index(col)
                visible = not self.isColumnHidden(idx)
                act = menu.addAction(f"{'Hide' if visible else 'Show'} {col}")
                def mk_toggle(i=idx, v=visible):
                    return lambda: self.setColumnHidden(i, v)
                act.triggered.connect(mk_toggle())

        if menu.actions():
            menu.exec(self.mapToGlobal(pos))

    def get_selected_file_path(self) -> Optional[str]:
        """Get the currently selected file path."""
        return self._model.selected_file
