"""
Unified file information model for Update Data module.

This module provides the FileInfoData dataclass that bridges the gap between
FileInfoService output and UI display needs, consolidating all file-related
data structures into a single, consistent model.
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime
from pathlib import Path


@dataclass
class FileInfoData:
    """
    Unified file information model bridging FileInfoService and UI needs.
    
    This dataclass consolidates file information from multiple sources:
    - FileInfoService provides: path, bank_type, format_type, handler, size_bytes, size_str
    - UI needs: modified, created, is_valid, is_processed for display and state management
    
    Attributes:
        path: Full file path
        bank_type: Bank name from handler detection (e.g., "Kiwibank", "Unknown")
        format_type: Format variant from handler (e.g., "Basic CSV", "Unrecognized")
        handler: Handler class name if detected, None otherwise
        size_bytes: File size in bytes
        size_str: Human-readable size string (e.g., "1.2 MB")
        modified: File modification timestamp
        created: File creation timestamp
        is_valid: Whether file is valid for processing
        is_processed: Whether file has been processed
    """
    # From FileInfoService
    path: str
    bank_type: str
    format_type: str
    handler: Optional[str]
    size_bytes: int
    size_str: str
    
    # For UI compatibility and state management
    modified: Optional[datetime] = None
    created: Optional[datetime] = None
    is_valid: bool = True
    is_processed: bool = False
    
    @property
    def name(self) -> str:
        """Get the filename without path."""
        return Path(self.path).name
    
    @property
    def size_formatted(self) -> str:
        """Get human-readable file size (uses FileInfoService formatting)."""
        return self.size_str
    
    @property
    def file_info_display(self) -> str:
        """Get formatted file info for display (e.g., 'Kiwibank | Basic CSV')."""
        if self.bank_type == "Unknown" and self.format_type == "Unrecognized":
            # For unrecognized files, show just the extension
            ext = Path(self.path).suffix.upper()
            return f"{ext} File" if ext else "Unknown File"
        elif self.bank_type == "Unknown":
            return self.format_type
        elif self.format_type == "Unrecognized":
            return self.bank_type
        else:
            return f"{self.bank_type} | {self.format_type}"
    
    @property
    def created_formatted(self) -> str:
        """NZ-style two-digit year and 12-hour time with am/pm: DD/MM/YY hh:mm am/pm"""
        dt = self.created or self.modified
        if dt:
            try:
                return dt.strftime("%d/%m/%y %I:%M %p").lower()
            except Exception:
                return ""
        return ""
    
    @classmethod
    def from_service_data(cls, service_data: dict, **kwargs):
        """
        Create FileInfoData from FileInfoService.get_file_info() output.
        
        Args:
            service_data: Dictionary from FileInfoService containing file info
            **kwargs: Additional fields to set (modified, created, is_valid, etc.)
            
        Returns:
            FileInfoData instance with service data and any additional fields
        """
        return cls(
            path=service_data['path'],
            bank_type=service_data['bank_type'],
            format_type=service_data['format_type'],
            handler=service_data['handler'],
            size_bytes=service_data['size_bytes'],
            size_str=service_data['size_str'],
            **kwargs
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary for serialization or compatibility."""
        return {
            'path': self.path,
            'bank_type': self.bank_type,
            'format_type': self.format_type,
            'handler': self.handler,
            'size_bytes': self.size_bytes,
            'size_str': self.size_str,
            'modified': self.modified,
            'created': self.created,
            'is_valid': self.is_valid,
            'is_processed': self.is_processed,
            'name': self.name,
            'size_formatted': self.size_formatted,
            'file_info_display': self.file_info_display,
            'created_formatted': self.created_formatted
        }
