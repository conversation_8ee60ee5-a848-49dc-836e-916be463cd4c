# Update Data UI Event Contracts (Authoritative)

Status: Canonical registry for Update Data UI intents and typed state events
Scope: View-as-switchboard pattern, local event bus channels, dialog policy, FileSelector API cheatsheet
Applies to: fm.modules.update_data UI, presenters, and managers

**Last Updated: 2025-08-07 - Reflects FileInfoManager refactoring with enriched data flow**

Cross-Links:
- Canonical How-To: [FileSelector-API-HowTo.md](flatmate/DOCS/_GUIDES/FileSelector-API-HowTo.md)
- Deep-dive: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- Testing protocol: [testing_protocol.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md)

---

## 1) Principles

- View is the central switchboard: Qt signals → canonical Local Bus intents
- Managers/Presenter subscribe to intents; emit typed state events for UI rendering
- One canonical rendering path: typed events drive UI; avoid parallel direct updates
- Dialogs shown only from dialog-request events, not directly by presenter or sub-widgets
- Initial morphing/setup may use presenter→view interface calls when not duplicating event-render logic

---

## 2) Namespaces and Ownership

Intents (string channels, Local Event Bus):
- Prefix: fm.update_data.intent.*
- Emitted by: View (translation from Qt signals)
- Consumed by: Presenter/Managers

Typed State Events (dataclasses in ui_events.py or equivalent):
- Prefix: fm.update_data.state.*
- Emitted by: Managers/Presenter when appropriate
- Consumed by: View (render) and any observers (e.g., status bar)

Dialog Requests (typed events):
- Prefix: fm.update_data.dialog.*
- Emitted by: Managers/Presenter
- Consumed by: View (QMessageBox or equivalent handler)

---

## 3) Canonical Channels

Intents
- fm.update_data.intent.SOURCE_SELECT_REQUESTED
  - payload: enum value "SELECT_FILES" | "SELECT_FOLDER"
  - Emit: View (Left Panel signal)
  - Consume: Presenter → FileManager
- fm.update_data.intent.add_files_requested
  - payload: none or {"initial_dir": str}
  - Emit: View (UDFileView.add_files_requested)
  - Consume: FileManager
- fm.update_data.intent.PROCESS_REQUESTED
  - payload: options dict or specific struct
  - Emit: View (e.g., a button in Left Panel)
  - Consume: Presenter → ProcessingManager
- fm.update_data.intent.CANCEL_REQUESTED
  - payload: none
  - Emit: View
  - Consume: ProcessingManager
- fm.update_data.intent.OPTION_CHANGED
  - payload: {"key": str, "value": any}
  - Emit: View (from option widgets)
  - Consume: Presenter/Managers

Typed State Events
- fm.update_data.state.FileListUpdated
  - dataclass: FileListUpdatedEvent(files: List[FileInfoData], source_path: str)
  - Emit: FileInfoManager (renamed from FileListManager)
  - Consume: View (render files pane with enriched data)
  - Data: Enriched FileInfoData objects with bank_type, format_type, handler info
- fm.update_data.state.ProcessingStarted
  - dataclass: ProcessingStartedEvent(meta: dict | None)
  - Emit: ProcessingManager
  - Consume: View (disable inputs/update status)
- fm.update_data.state.ProcessingCompleted
  - dataclass: ProcessingCompletedEvent(result: dict | None)
  - Emit: ProcessingManager
  - Consume: View (enable inputs/update status)
- fm.update_data.state.FileAdded
  - dataclass: FileAddedEvent(file_info: FileInfoData)
  - Emit: FileInfoManager
  - Consume: View (individual file addition with enriched data)
- fm.update_data.state.FileRemoved
  - dataclass: FileRemovedEvent(file_path: str)
  - Emit: FileInfoManager
  - Consume: View

Dialog Requests
- fm.update_data.dialog.ErrorRequested
  - dataclass: DialogRequestEvent(kind="error", title: str, message: str, details: dict | None)
  - Emit: Managers/Presenter
  - Consume: View (QMessageBox critical)
- fm.update_data.dialog.SuccessRequested
  - dataclass: DialogRequestEvent(kind="success", title: str, message: str, details: dict | None)
  - Emit: Managers/Presenter
  - Consume: View (QMessageBox information)

Note: Use typed dataclasses for all state/dialog events; avoid untyped dicts.

---

## 4) FileInfoManager Architecture (The Librarian Pattern)

### Core Components:
- **FileInfoManager** (The Librarian)
  - Role: Single source of truth for enriched file data
  - Data: Maintains List[FileInfoData] with bank_type, format_type, handler info
  - Enrichment: Uses FileInfoService to enrich files when added/changed
  - Events: Publishes FileListUpdatedEvent with enriched data
  - Location: _ui/_presenter/file_info_manager.py

- **FileConfigManager** (The Gatekeeper)
  - Role: Handles file/folder selection and configuration
  - Responsibilities: Dialog management, recent folders, source options
  - Integration: Delegates file list operations to FileInfoManager
  - Configuration: Persists source options via ud_keys.Source.LAST_SOURCE_OPTION

### Data Flow:
```
User Selection → FileConfigManager → FileInfoManager → FileInfoService
                                         ↓
FileListUpdatedEvent(List[FileInfoData]) → View → UDFileView.set_enriched_files()
                                                      ↓
                                                  FileTree (displays enriched data)
```

### Key Contracts:
- FileInfoManager MUST enrich files using FileInfoService before emitting events
- Events MUST contain List[FileInfoData] objects, not plain file paths
- UI components MUST handle enriched data via set_enriched_files() method
- Backward compatibility maintained for legacy plain file path handling

---

## 5) Separation: Left Panel vs File Pane

- Left Panel
  - Emits SOURCE_SELECT_REQUESTED with value SELECT_FILES/SELECT_FOLDER
  - FileConfigManager handles dialogs and delegates to FileInfoManager
  - Source options persist via config system (no auto-dialogs on option change)
- File Pane (UDFileView)
  - Emits add_files_requested
  - FileConfigManager subscribes and opens files dialog
  - Receives enriched data via set_enriched_files() method
  - FileTree displays bank names, format types in Status column

This avoids double-dialog collisions by separating channels and prevents auto-dialogs during initialization.

---

## 5) Presenter vs Event Decision Tree

Allowed direct interface calls (Presenter → View):
- Initial morphing/setup steps
- One-off non-runtime adjustments that do not duplicate event-driven rendering

Must be event-driven:
- Runtime processing lifecycle (enable/disable, progress, completion)
- User feedback dialogs (error/success) via dialog-request events
- Canonical file list updates (render via FileListUpdatedEvent with enriched data)
- File enrichment and metadata display (via FileInfoManager → FileInfoService)

Forbidden:
- Presenter or sub-widgets directly showing dialogs
- Sub-widgets calling presenter methods to trigger dialogs without going through intents and dialog-request events

If an exception is needed temporarily:
- Document it in this file with rationale and a convergence plan to events

---

## 6) File Selection API Cheatsheet

FileSelector.get_paths(selection_type: Literal["files","folder"], initial_dir: str | None, title: str, parent: QWidget)

Usage:
- SOURCE_SELECT_REQUESTED (SELECT_FILES)
  - FileManager._select_files() → get_paths("files", ...)
- SOURCE_SELECT_REQUESTED (SELECT_FOLDER)
  - FileManager._select_folder() → get_paths("folder", ...)
- add_files_requested
  - FileManager handles similarly to _select_files; pushes results through FileListManager

Persistence:
- Last directory managed via ud_config/ud_keys
- Avoid duplication of last-dir logic across widgets/managers

---

## 7) Payload Guidelines

- Use dataclasses for typed events
- Prefer enums for constrained values (e.g., SELECT_FILES/SELECT_FOLDER)
- Keep payloads minimal and specific to the event
- Document any field additions here and update consumers accordingly

---

## 8) Verification Checklist (Operational)

- Exactly one dialog per action:
  - Left Panel select → one dialog
  - File Pane add → one dialog
- Single rendering path:
  - File list UI updates only from FileListUpdatedEvent
- Processing lifecycle:
  - View responds to ProcessingStarted/Completed; no duplicate direct UI toggles
- Logging:
  - Log subscriptions and key handler paths for SOURCE_SELECT_REQUESTED and add_files_requested

---

## 9) References (clickable)

- View wiring: [ud_view.py](flatmate/src/fm/modules/update_data/_ui/ud_view.py)
- FileManager subscribe: [file_management.py.subscribe_add_files_channel()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:96)
- FileManager handler: [file_management.py._on_files_add_requested()](flatmate/src/fm/modules/update_data/_ui/_presenter/file_management.py:110)
- File list manager: [file_list_manager.py](flatmate/src/fm/modules/update_data/_ui/_presenter/file_list_manager.py)
- Local event bus: [local_event_bus.py](flatmate/src/fm/modules/update_data/services/local_event_bus.py)
- Typed UI events (module): [ui_events.py](flatmate/src/fm/modules/update_data/_ui/ui_events.py)
- Deep-dive guide: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
- FileSelector How-To (canonical): [FileSelector-API-HowTo.md](flatmate/DOCS/_GUIDES/FileSelector-API-HowTo.md)
- Testing protocol: [testing_protocol.md](flatmate/DOCS/_PROTOCOLS/GUIDES/testing_protocol.md)

---

## 10) Known Exceptions (temporary, if any)

- None. Any future exception must be listed here with:
  - Rationale
  - Scope and duration
  - Convergence plan back to event-first