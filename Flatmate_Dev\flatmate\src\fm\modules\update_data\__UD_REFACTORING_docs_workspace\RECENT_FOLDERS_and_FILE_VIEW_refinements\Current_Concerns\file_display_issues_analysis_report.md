# File Display Issues Analysis Report (Post-Refactor Update)

Date: 2025-08-07
Status: Updated after focused FileTree refactor
Scope: Current FileTree implementation vs Original FileDisplayWidget, aligned with Update-Data UI guide

---

## Executive Summary

The focused FileTree implementation has addressed major UX pain points by:
- Limiting hierarchy to grandparent → parent → file chains
- Fixing column sizing with a column width policy
- Preserving enriched file info rendering

Remaining issues and refinements are documented below with concrete, code-anchored actions.

References:
- Current widget: [`python.class FileTree(QTreeWidget)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:25)
- Focused builder: [`python.def _refresh_data_focused(self, depth: int = 2)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:144)
- Column policy: [`python.def _apply_column_width_policy(self)`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:251)
- View model: [`python.class FileViewModel`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/models.py:14)
- UI Guide: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)

---

## Current Issues (Validated Against Code and Notes)

1) Column header names and order
- From notes: Desired order “Selected Files, File Info, Size, Created” and drop “Type”.
- Code today: ["Name", "Size", "File Info", "Created"] (+ optional "Type") in [`python.def _setup_tree`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:46).
- Resolution:
  - Rename header “Name” → “Selected Files”
  - Reorder to ["Selected Files", "File Info", "Size", "Created"]
  - Default: omit “Type” unless explicitly enabled by config

2) Stretching and resizability
- Fixed: Name column now uses Stretch in [`python.def _apply_column_width_policy`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:265).
- Actions:
  - Ensure “File Info” remains Interactive with width ~240 in focused mode
  - Persist user-resized widths via header.sectionResized hook (store in ud_config), restore on init

3) File Info text formatting
- Current: fi.file_info_display used as-is ([`python.def _add_file_item`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:364)), sometimes “Kiwibank | Basic CSV”.
- Action:
  - Normalize at source (FileInfoData builder) to “Kiwibank Basic CSV” (no pipes)
  - Temporary UI guard: replace " | " → " " before setText

4) Folder label separators and truncation
- Current display built by [`python.def _get_display_folder_name`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:319) using Path.parts.
- Actions:
  - Use forward slashes for display (Path(...).as_posix()) to avoid Windows “\” mix
  - Keep home-relative (“~/...”) and two-part fallback (“.../parent/leaf”)

5) Created date shows “Unknown”
- UI reads fi.created; root cause likely upstream metadata gap for some files on Windows.
- Action:
  - Ensure FileInfo population path uses reliable platform-specific created time with fallbacks to modified.
  - Once populated, UI line already formats consistently [%d/%m/%Y %H:%M].

6) Recent folders integration in Source selector
- State: FileConfigManager tracks/persists recents ([`python.class FileConfigManager`](flatmate/src/fm/modules/update_data/_ui/_presenter/file_config_manager.py:31)).
- Action:
  - Add dynamic items to the left panel SourceOptions control for recent_source_folders, emitting canonical intents per the UI guide.

7) Event/channel conformance
- Verify continued split between add_files_requested (file pane) vs SOURCE_SELECT_REQUESTED (left panel), matching the UI guide sections 3, 5, 10.

---

## Concrete Change Plan

A) Headers/Oder
- Where: [`python.def _setup_tree`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:46)
- Changes:
  - self._columns = ["Selected Files", "File Info", "Size", "Created"]
  - Update all index lookups accordingly
  - Default: hide “Type” unless config flag set

B) Column policy and persistence
- Where: [`python.def _apply_column_width_policy`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:251)
- Ensure modes:
  - Selected Files: Stretch
  - File Info: Interactive; focused width ~240
  - Size/Created: ResizeToContents
- Persist widths:
  - Connect header.sectionResized to store per-column size, restore before applying policy

C) File Info normalization
- Prefer source fix (FileInfoData/file_info_display). Fallback: sanitize in UI setter.

D) Folder label normalization
- Use as_posix for display-only strings, keep underlying path data untouched.

E) Created timestamp robustness
- Address at FileInfo population; UI code remains unchanged once data is correct.

F) Recent folders UI
- Add to Source selector with clear visual separation and correct intent emission.

---

## Verification Checklist

- Headers exactly: “Selected Files”, “File Info”, “Size”, “Created”
- “Selected Files” stretches; “File Info” resizable; widths persisted across runs
- Folder labels: “~/...” or “…/parent/leaf” with forward slashes
- File Info: “Kiwibank Basic CSV” (no pipes)
- Created shows formatted timestamp for normal files
- Recent folders appear and function via canonical intents
- Focused view remains: grandparent → parent → files across disparate sources

---

## References

- Focused chains builder: [`python.def _refresh_data_focused`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:144)
- Column policy: [`python.def _apply_column_width_policy`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:251)
- Folder label builder: [`python.def _get_display_folder_name`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:319)
- Add file row: [`python.def _add_file_item`](flatmate/src/fm/modules/update_data/_ui/_view/center_panel_components/file_pane_v2/widgets/file_tree.py:346)
- UI guide: [Update-Data-UI-Deep-Dive.md](flatmate/DOCS/_GUIDES/Update-Data-UI-Deep-Dive.md)
