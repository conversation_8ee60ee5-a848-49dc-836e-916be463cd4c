"""CenterPanelManager: pure layout/container for Update Data module.

Scope:
- Composite container owning the vertical splitter with GuidePane (top) and UDFileView (bottom).
- No domain/user-intent signal emission or translation. The UpdateDataView is the switchboard and bridges
  widget Qt signals to the Local Event Bus.
- Provides a minimal interface for View/Presenter to target UI updates: set_files, get_files,
  display_enriched_file_info, display_welcome, show_error, show_success.
"""


from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtWidgets import QVBoxLayout, QSplitter
from PySide6.QtCore import Qt, QSettings
import pandas as pd

from fm.gui._shared_components.base.base_panel_component import BasePanelComponent

from .center_panel_components import guide_pane
from .center_panel_components import UDFileView

from fm.core.services.logger import log
from fm.core.config.paths import AppPaths


class CenterPanelManager(BasePanelComponent):
    """Main center panel manager for the Update Data module.

    This class uses the Composite pattern to manage different panes
    that can be shown in the center area of the Update Data module.
    """

    # Signals removed: domain/user-intent propagation is handled by UpdateDataView via Local Event Bus

    def __init__(self, parent=None):
        """Initialize the center panel manager."""
        super().__init__(parent)
        self.info_bar = None
        self.event_bus = global_event_bus
        # Source metadata (for display only; MUST NOT filter file list)
        self._current_source_path: str = ""
        self._init_ui()
        # Purposely no signal wiring here; UpdateDataView is the switchboard
        # UI prefs: prepare settings handle (widgets-only, isolated from domain config)
        try:
            AppPaths.ensure_dir_exists(AppPaths.CONFIG_DIR)
            self._ui_prefs = QSettings(str(AppPaths.CONFIG_DIR / "ui_prefs.ini"), QSettings.Format.IniFormat)
        except Exception as e:
            self._ui_prefs = None
            log.debug(f"[CenterPanelManager] UI prefs disabled (cannot init QSettings): {e}")

    def _init_ui(self):
        """Initialize the UI components with resizable split layout."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # Create vertical splitter for guide pane (top) and file display (bottom)
        self.splitter = QSplitter(Qt.Vertical, self)
        self.main_layout.addWidget(self.splitter)

        self._create_panes()

        # Layout behavior:
        # - Guide pane area stays tight; readable padding is around its content only
        # - File view expands to take remaining space
        try:
            from PySide6.QtWidgets import QSizePolicy
            self.splitter.setStretchFactor(0, 0)  # Guide container: minimal stretch
            self.splitter.setStretchFactor(1, 1)  # File view: expands
        except Exception:
            pass

        # Bias sizes to favor file view; guide area remains compact
        # Try restore from UI prefs; fallback to default
        try:
            restored = False
            if self._ui_prefs:
                sizes = self._ui_prefs.value("update_data/center_splitter", None)
                if isinstance(sizes, list) and all(isinstance(x, int) for x in sizes) and len(sizes) == 2:
                    self.splitter.setSizes(sizes)
                    restored = True
            if not restored:
                self.splitter.setSizes([180, 820])
        except Exception:
            self.splitter.setSizes([180, 820])

    def _create_panes(self):
        """Create panes and add them to the splitter."""
        # Guide pane (top half) - contextual messages and instructions
        self.guide_pane = guide_pane.GuidePaneWidget()
        try:
            # Ensure readable padding on the guide pane without affecting the file view expansion
            from PySide6.QtWidgets import QVBoxLayout, QWidget
            wrapper = QWidget(self.splitter)
            v = QVBoxLayout(wrapper)
            v.setContentsMargins(12, 12, 12, 12)  # readable padding
            v.setSpacing(6)
            v.addWidget(self.guide_pane)
            self.splitter.addWidget(wrapper)
        except Exception:
            # Fallback: add guide pane directly if wrapper fails
            self.splitter.addWidget(self.guide_pane)

        # File display pane (bottom half) - file list and management
        self.file_pane = UDFileView()
        self.splitter.addWidget(self.file_pane)

        # Ensure file pane expands
        try:
            from PySide6.QtWidgets import QSizePolicy
            self.file_pane.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        except Exception:
            pass

    def closeEvent(self, event):
        """Persist splitter sizes to UI prefs on close."""
        try:
            if self._ui_prefs and hasattr(self, "splitter") and self.splitter is not None:
                sizes = self.splitter.sizes()
                # Ensure list of ints
                if isinstance(sizes, list) and all(isinstance(x, int) for x in sizes):
                    self._ui_prefs.setValue("update_data/center_splitter", sizes)
        except Exception as e:
            log.debug(f"[CenterPanelManager] Failed to save UI splitter sizes: {e}")
        # Call parent to continue normal close handling
        try:
            super().closeEvent(event)
        except Exception:
            pass

    # === Public interface used by UpdateDataView ===
    def set_files(self, file_paths: list[str], source_path: str = "") -> None:
        """
        Set files into the UDFileView. Source path is metadata only and MUST NOT be used to filter.
        This method is invoked via UDView.update_files_display on FILE_LIST_UPDATED events.
        """
        try:
            self._current_source_path = source_path or ""
            if hasattr(self, "file_pane") and self.file_pane is not None:
                # Forward the entire list as-is
                self.file_pane.set_files(file_paths)
                log.debug(f"[CENTER_PANEL] set_files -> forwarded {len(file_paths)} files (source='{self._current_source_path}')")
            else:
                log.warning("[CENTER_PANEL] file_pane not available to set files")
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error in set_files: {e}")

    def get_files(self) -> list[str]:
        """Get current files from the file pane."""
        try:
            if hasattr(self, "file_pane") and self.file_pane is not None:
                return self.file_pane.get_files()
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error getting files: {e}")
        return []

    def set_source_path(self, source_path: str) -> None:
        """Optionally display the source path in the guide pane; never filter files."""
        try:
            self._current_source_path = source_path or ""
            if hasattr(self, "guide_pane") and self.guide_pane is not None and self._current_source_path:
                # Non-intrusive: show hint message; avoid overwriting other guide content if not empty
                try:
                    self.guide_pane.display(f"Source: {self._current_source_path}")
                except Exception:
                    pass
        except Exception as e:
            log.debug(f"[CENTER_PANEL] Non-critical: set_source_path failed: {e}")

    def display_enriched_file_info(self, file_info_list) -> None:
        """Forward enriched file info to the UDFileView."""
        try:
            if hasattr(self, "file_pane") and self.file_pane is not None:
                self.file_pane.display_enriched_file_info(file_info_list)
        except Exception as e:
            log.error(f"[CENTER_PANEL] Error displaying enriched file info: {e}")
