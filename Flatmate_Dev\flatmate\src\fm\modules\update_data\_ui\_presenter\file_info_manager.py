"""
File info management for Update Data module.

The FileInfoManager is "The Librarian" - the single source of truth for the file list
and all associated enriched information (bank, type, format, etc.).

Responsibilities:
- Holds the master list of FileInfoData objects
- Uses FileInfoService to enrich data when the list changes
- Publishes FileListUpdatedEvent with complete, rich data
- Integrates with folder monitoring for file discovery

This was renamed from file_list_manager.py and completely rewritten to use
the unified FileInfoData model as originally planned.
"""

import os
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

from fm.core.services.logger import log
from ..ui_events import FileListUpdatedEvent, FileAddedEvent, FileRemovedEvent
from ...services.local_event_bus import ViewEvents
from ...models.file_info import FileInfoData


@dataclass
class MonitoredFolder:
    """Simple dataclass for folder monitoring status as requested by user."""
    path: str
    monitor_new_files: bool = False
    last_scan: Optional[datetime] = None
    file_count: int = 0


class FileInfoManager:
    """
    The Librarian - Single source of truth for file list and enriched information.

    This class manages the canonical list of FileInfoData objects, enriching files
    with bank_type, format_type, handler info, and other metadata using FileInfoService.

    Responsibilities:
    - Holds the master list of FileInfoData objects (enriched file data)
    - Uses FileInfoService to enrich data when files are added
    - Publishes FileListUpdatedEvent with complete, rich data
    - Manages monitored folders for file discovery
    - Integrates with existing FolderMonitorService

    Does NOT: Handle user clicks, show dialogs, or start processing - only manages data.
    """

    def __init__(self, folder_monitor_service, local_bus, file_info_service):
        """
        Initialize the file info manager.

        Args:
            folder_monitor_service: Core folder monitoring service
            local_bus: Local event bus for module communication
            file_info_service: Service for file enrichment and metadata
        """
        self.folder_monitor_service = folder_monitor_service
        self.local_bus = local_bus
        self.file_info_service = file_info_service

        # Master list of enriched file data - THE SINGLE SOURCE OF TRUTH
        self.file_info_list: List[FileInfoData] = []

        # Keep plain paths for backward compatibility and quick lookups
        self.file_paths_list: List[str] = []

        # Monitored folders - simple dict as user suggested
        self.monitored_folders: Dict[str, MonitoredFolder] = {}

        # Subscribe to relevant events
        self._subscribe_to_events()

        log.debug("[FILE_INFO_MANAGER] Initialized with empty enriched file list")
    
    def _subscribe_to_events(self):
        """Subscribe to events that affect file list management."""
        # Subscribe to folder monitoring events
        self.local_bus.subscribe(ViewEvents.FOLDER_MONITORING_TOGGLED.value,
                               self._on_folder_monitoring_toggled)
        
        # Subscribe to file discovery events from folder monitoring
        self.local_bus.subscribe(ViewEvents.FILES_DISCOVERED.value,
                               self._on_files_discovered)

        # Subscribe to remove intent (published by View via Local Bus)
        # Canonical remove flow: UDFileView.remove_file -> View publishes FILE_REMOVED(FileRemovedEvent) -> here
        self.local_bus.subscribe(ViewEvents.FILE_REMOVED.value, self._on_file_removed_intent)
    
    def set_files(self, file_paths: List[str], source_path: str = ""):
        """
        Set the canonical file list with enrichment and emit update event.

        This is the core method that enriches files using FileInfoService and
        stores both plain paths and enriched FileInfoData objects.

        Args:
            file_paths: List of file paths to set
            source_path: Source directory path for context
        """
        try:
            log.debug(f"[FILE_INFO_MANAGER] Setting {len(file_paths)} files with enrichment")

            # Store plain paths for backward compatibility
            self.file_paths_list = file_paths.copy() if file_paths else []

            # Enrich files using FileInfoService - THE KEY IMPLEMENTATION
            if self.file_paths_list:
                log.debug(f"[FILE_INFO_MANAGER] Enriching {len(self.file_paths_list)} files...")
                enriched_data = self.file_info_service.discover_files(self.file_paths_list)

                # Convert to FileInfoData objects
                self.file_info_list = [
                    FileInfoData.from_service_data(info) for info in enriched_data
                ]
                log.debug(f"[FILE_INFO_MANAGER] Successfully enriched {len(self.file_info_list)} files")
            else:
                self.file_info_list = []
                log.debug("[FILE_INFO_MANAGER] No files to enrich")

            # Update monitored folder if source is a directory
            if source_path and os.path.isdir(source_path):
                self._update_monitored_folder(source_path, len(file_paths))

            # Emit event with ENRICHED DATA
            self._emit_list_updated(source_path)

        except Exception as e:
            log.error(f"[FILE_INFO_MANAGER] Error setting files: {e}")
            # On error, clear both lists to maintain consistency
            self.file_paths_list = []
            self.file_info_list = []
    
    def add_files(self, new_files: List[str]) -> bool:
        """
        Add files to the canonical list with enrichment.

        Args:
            new_files: List of file paths to add

        Returns:
            bool: True if files were added, False otherwise
        """
        try:
            if not new_files:
                return False

            # Avoid duplicates
            unique_files = [f for f in new_files if f not in self.file_paths_list]

            if unique_files:
                # Add to plain paths list
                self.file_paths_list.extend(unique_files)

                # Enrich new files and add to enriched list
                log.debug(f"[FILE_INFO_MANAGER] Enriching {len(unique_files)} new files...")
                enriched_data = self.file_info_service.discover_files(unique_files)
                new_file_info = [
                    FileInfoData.from_service_data(info) for info in enriched_data
                ]
                self.file_info_list.extend(new_file_info)

                # Emit list updated with all enriched data
                self._emit_list_updated()

                # Emit individual file added events with enriched data
                for file_info in new_file_info:
                    self.local_bus.emit(ViewEvents.FILE_ADDED.value,
                                      FileAddedEvent(file_info=file_info))

                log.debug(f"[FILE_INFO_MANAGER] Added {len(unique_files)} new enriched files")
                return True
            else:
                log.debug("[FILE_INFO_MANAGER] No new files to add (duplicates filtered)")
                return False

        except Exception as e:
            log.error(f"[FILE_INFO_MANAGER] Error adding files: {e}")
            return False
    
    def remove_file(self, file_path: str) -> bool:
        """
        Remove file from both canonical lists (paths and enriched data).

        Args:
            file_path: Path of file to remove

        Returns:
            bool: True if file was removed, False otherwise
        """
        try:
            if file_path in self.file_paths_list:
                # Remove from plain paths list
                self.file_paths_list.remove(file_path)

                # Remove from enriched data list
                self.file_info_list = [
                    info for info in self.file_info_list if info.path != file_path
                ]

                # Emit updated list with remaining enriched data
                self._emit_list_updated()

                # Emit file removed event
                self.local_bus.emit(ViewEvents.FILE_REMOVED.value,
                                  FileRemovedEvent(file_path=file_path))

                log.debug(f"[FILE_INFO_MANAGER] Removed file: {os.path.basename(file_path)}")
                return True
            else:
                log.warning(f"[FILE_INFO_MANAGER] File not in list: {file_path}")
                return False
                
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error removing file: {e}")
            return False
    
    def get_files(self) -> List[str]:
        """
        Get copy of canonical file paths list (for backward compatibility).

        Returns:
            List[str]: Copy of current file paths
        """
        return self.file_paths_list.copy()

    def get_file_info_list(self) -> List[FileInfoData]:
        """
        Get copy of enriched file info list - THE MAIN DATA SOURCE.

        Returns:
            List[FileInfoData]: Copy of current enriched file data
        """
        return self.file_info_list.copy()

    def get_file_count(self) -> int:
        """Get current file count."""
        return len(self.file_paths_list)

    def clear_files(self):
        """Clear both file lists."""
        self.file_paths_list.clear()
        self.file_info_list.clear()
        self._emit_list_updated()
        log.debug("[FILE_INFO_MANAGER] Cleared both file lists")
    
    def set_folder_monitoring(self, folder_path: str, enabled: bool):
        """
        Set monitoring status for a folder.
        
        Args:
            folder_path: Path to folder
            enabled: Whether monitoring should be enabled
        """
        try:
            folder_path = str(Path(folder_path).resolve())
            
            if folder_path in self.monitored_folders:
                self.monitored_folders[folder_path].monitor_new_files = enabled
            else:
                self.monitored_folders[folder_path] = MonitoredFolder(
                    path=folder_path, 
                    monitor_new_files=enabled,
                    last_scan=datetime.now() if enabled else None
                )
            
            # Update FolderMonitorService
            self.folder_monitor_service.set_folder_monitored(folder_path, enabled)
            
            log.debug(f"[FILE_LIST_MANAGER] Folder monitoring {'enabled' if enabled else 'disabled'} for: {folder_path}")
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error setting folder monitoring: {e}")
    
    def get_monitored_folders(self) -> Dict[str, MonitoredFolder]:
        """Get copy of monitored folders dict."""
        return self.monitored_folders.copy()
    
    def is_folder_monitored(self, folder_path: str) -> bool:
        """Check if a folder is being monitored."""
        folder_path = str(Path(folder_path).resolve())
        folder_info = self.monitored_folders.get(folder_path)
        return folder_info.monitor_new_files if folder_info else False
    
    def _update_monitored_folder(self, folder_path: str, file_count: int):
        """Update monitored folder information."""
        try:
            folder_path = str(Path(folder_path).resolve())
            
            if folder_path in self.monitored_folders:
                self.monitored_folders[folder_path].file_count = file_count
                self.monitored_folders[folder_path].last_scan = datetime.now()
            else:
                # Add as known folder but not monitored by default
                self.monitored_folders[folder_path] = MonitoredFolder(
                    path=folder_path,
                    monitor_new_files=False,
                    last_scan=datetime.now(),
                    file_count=file_count
                )
            
            log.debug(f"[FILE_LIST_MANAGER] Updated folder info: {folder_path} ({file_count} files)")
            
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error updating monitored folder: {e}")
    
    def _emit_list_updated(self, source_path: str = ""):
        """Publish file list updated event with ENRICHED DATA."""
        try:
            # THE CRITICAL FIX: Send enriched FileInfoData objects, not plain paths
            event_data = FileListUpdatedEvent(
                files=self.file_info_list.copy(),  # ✅ ENRICHED DATA
                source_path=source_path
            )
            # PUBLISH on Local Bus (events are published, signals are emitted)
            self.local_bus.emit(ViewEvents.FILE_LIST_UPDATED.value, event_data)
            log.debug(f"[FILE_INFO_MANAGER] Published FILE_LIST_UPDATED with {len(self.file_info_list)} enriched files")

        except Exception as e:
            log.error(f"[FILE_INFO_MANAGER] Error publishing list updated event: {e}")
    
    def _on_folder_monitoring_toggled(self, event_data):
        """Handle folder monitoring toggle events."""
        try:
            if hasattr(event_data, 'folder_path') and hasattr(event_data, 'enabled'):
                self.set_folder_monitoring(event_data.folder_path, event_data.enabled)
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error handling folder monitoring toggle: {e}")
    
    def _on_files_discovered(self, event_data):
        """Handle files discovered from folder monitoring."""
        try:
            if hasattr(event_data, 'files') and event_data.files:
                # Add discovered files to the list
                self.add_files(event_data.files)
                log.debug(f"[FILE_LIST_MANAGER] Added {len(event_data.files)} discovered files")
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error handling discovered files: {e}")

    def _on_file_removed_intent(self, event_data):
        """Handle a published file remove intent from the View."""
        try:
            file_path = None
            if hasattr(event_data, 'file_path'):
                file_path = event_data.file_path
            elif isinstance(event_data, dict):
                file_path = event_data.get('file_path')

            if file_path:
                self.remove_file(file_path)
            else:
                log.warning("[FILE_LIST_MANAGER] FILE_REMOVED intent received with no file_path")
        except Exception as e:
            log.error(f"[FILE_LIST_MANAGER] Error handling file removed intent: {e}")
