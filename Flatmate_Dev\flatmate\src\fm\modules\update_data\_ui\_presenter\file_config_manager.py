"""
File configuration management for Update Data module.

This module manages file/folder selection and configuration including:
- File and folder selection dialogs
- Save location selection and "same as source" logic
- Recent folders management (Quick Access)
- Source and save option changes

Renamed from file_management.py as part of Phase 3 refactoring.
"""

import os
from pathlib import Path
from typing import TYPE_CHECKING, List

from fm.core.services.logger import log
from fm.modules.update_data.models.config import SourceOptions, SaveOptions
from fm.modules.update_data._ui.ui_events import SourceDiscoveredEvent, FileDisplayUpdateEvent
from fm.modules.update_data.services.local_event_bus import ViewEvents
from ...config.ud_keys import UpdateDataKeys as ud_keys
from ...config.ud_config import ud_config
from .._view.shared_components.file_selector import FileSelector, FileUtils


if TYPE_CHECKING:
    from ..interface import IUpdateDataView
    from .state_coordinator import UpdateDataState, StateManager


class FileConfigManager:
    """
    Manages file/folder selection configuration and recent folders.

    This class handles:
    - File and folder selection dialogs
    - Save location selection and "same as source" functionality
    - Recent folders management (Quick Access)
    - Source and save option changes
    
    Note: File enrichment and canonical file list management is now handled
    by FileListManager as part of the Phase 1 refactoring.
    """

    def __init__(self, view: 'IUpdateDataView', state_manager: 'StateManager',
                 file_info_manager, local_bus):
        """
        Initialize the file configuration manager.

        Args:
            view: The view interface
            state_manager: Consolidated state and UI sync manager
            file_info_manager: FileInfoManager for canonical file list operations
            local_bus: Local event bus for module events
        """
        self.view = view
        self.state_manager = state_manager
        self.state = state_manager.state
        self.file_info_manager = file_info_manager
        self.local_bus = local_bus

        # Prevent auto-dialogs during initialization
        self._initializing = True

        # Track selected source for "same as source" functionality
        self.selected_source = None

        # Load recent folders from config
        self.recent_source_folders = ud_config.get_value('recent_source_folders', [])
        self.max_recent_folders = ud_config.get_value('max_recent_folders', 5)

        # Restore last selected source option for persistence (Issue 3 fix)
        from ...config.ud_keys import UpdateDataKeys as ud_keys
        last_option = ud_config.get_value(ud_keys.Source.LAST_SOURCE_OPTION, SourceOptions.SELECT_FILES)
        self.state.source_option = last_option

        log.debug(f"[FILE_CONFIG_MANAGER] Initialized with recent folders support and restored option: {last_option}")

        # Subscribe to add-files channel
        self.subscribe_add_files_channel()

        # Initialization complete - allow normal operation
        self._initializing = False

    # =============================================================================
    # SOURCE SELECTION METHODS
    # =============================================================================

    def handle_source_option_change(self, option_data):
        """Handle source option change from UI - UPDATE OPTION ONLY, DON'T TRIGGER DIALOG."""
        try:
            # Extract the option from the event data
            if hasattr(option_data, 'option'):
                option = option_data.option
            else:
                option = option_data

            log.debug(f"[FILE_CONFIG_MANAGER] Source option changed: {option}")

            # Update state and save preference - DO NOT trigger file selection
            self.state.source_option = option

            # Save the last selected option to config for persistence
            from ...config.ud_keys import UpdateDataKeys as ud_keys
            ud_config.set_value(ud_keys.Source.LAST_SOURCE_OPTION, option)

            # Sync state to view to update UI
            self.state_manager.sync_state_to_view()

            log.debug(f"[FILE_CONFIG_MANAGER] Source option updated and saved: {option}")

        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling source option change: {e}")
    
    def handle_source_select(self, selection_type):
        """
        Handle source selection request from the view.
        
        Args:
            selection_type: Type of source selection (files/folder)
        """
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Source select requested: {selection_type}")
            
            if selection_type == SourceOptions.SELECT_FILES:
                self._select_files()
            elif selection_type == SourceOptions.SELECT_FOLDER:
                self._select_folder()
            else:
                log.warning(f"[FILE_CONFIG_MANAGER] Unknown selection type: {selection_type}")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in source selection: {e}")

    def _select_files(self):
        """Handle individual file selection."""
        try:
            # Prevent auto-dialogs during initialization (Issue 1 fix)
            if getattr(self, '_initializing', False):
                log.debug("[FILE_CONFIG_MANAGER] Skipping file dialog during initialization")
                return

            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, "")
            log.debug(f"[FILE_CONFIG_MANAGER] Opening file dialog with last_dir: {last_dir}")

            file_paths = FileSelector.get_paths(
                selection_type='files',
                initial_dir=last_dir,
                title="Select Files to Process",
                parent=None
            )
            
            if file_paths:
                log.debug(f"[FILE_CONFIG_MANAGER] Dialog returned file_paths: {file_paths}")
                
                # Update last directory
                folder_path = os.path.dirname(file_paths[0])
                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                
                # Update recent folders
                self._update_recent_folders(folder_path)
                
                self._process_selected_files(file_paths, 'files', folder_path)
            else:
                log.debug("[FILE_CONFIG_MANAGER] File selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in file selection: {e}")

    def _select_folder(self):
        """Handle folder selection."""
        try:
            # Prevent auto-dialogs during initialization (Issue 1 fix)
            if getattr(self, '_initializing', False):
                log.debug("[FILE_CONFIG_MANAGER] Skipping folder dialog during initialization")
                return

            last_dir = ud_config.get_value(ud_keys.Paths.LAST_SOURCE_DIR, "")
            log.debug(f"[FILE_CONFIG_MANAGER] Opening folder dialog with last_dir: {last_dir}")

            file_paths = FileSelector.get_paths(
                selection_type='folder',
                initial_dir=last_dir,
                title="Select Folder to Process",
                parent=None
            )
            
            if file_paths:
                log.debug(f"[FILE_CONFIG_MANAGER] Dialog returned {len(file_paths)} files from folder")

                # Get folder path from first file for config/recent folders
                folder_path = os.path.dirname(file_paths[0])

                # Update last directory and recent folders
                ud_config.set_value(ud_keys.Paths.LAST_SOURCE_DIR, folder_path)
                self._update_recent_folders(folder_path)

                self._process_selected_files(file_paths, 'folder', folder_path)
            else:
                log.debug("[FILE_CONFIG_MANAGER] Folder selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error in folder selection: {e}")

    def _process_selected_files(self, file_paths: List[str], source_type: str, source_path: str):
        """
        Process selected files by delegating to FileListManager.
        
        Args:
            file_paths: List of selected file paths
            source_type: Type of source ('files' or 'folder')
            source_path: Source directory path
        """
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Processing {len(file_paths)} selected files")
            
            # Store source info for "same as source" functionality
            self.selected_source = {
                "type": source_type,
                "path": source_path,
                "file_paths": file_paths
            }
            
            # Delegate to FileInfoManager for canonical list management and enrichment
            self.file_info_manager.set_files(file_paths, source_path)
            
            # Emit source discovered event
            self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value, 
                              SourceDiscoveredEvent(
                                  source_type=source_type,
                                  files=file_paths,
                                  path=source_path,
                                  count=len(file_paths)
                              ))
            
            log.debug(f"[FILE_CONFIG_MANAGER] Successfully processed {len(file_paths)} files")
            
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error processing selected files: {e}")

    # =============================================================================
    # RECENT FOLDERS MANAGEMENT (NEW - Phase 3)
    # =============================================================================
    
    def _update_recent_folders(self, folder_path: str):
        """
        Add a folder to the top of the recent list and persist it.
        
        Args:
            folder_path: Path to the folder to add
        """
        try:
            if not folder_path or not os.path.isdir(folder_path):
                return

            # Remove if already present to avoid duplicates and move to top
            if folder_path in self.recent_source_folders:
                self.recent_source_folders.remove(folder_path)
            
            # Add to the top
            self.recent_source_folders.insert(0, folder_path)

            # Trim the list to the max size
            self.recent_source_folders = self.recent_source_folders[:self.max_recent_folders]

            # Persist to config
            ud_config.set_value('recent_source_folders', self.recent_source_folders)
            
            # Update state for UI
            self.state.recent_source_folders = self.recent_source_folders.copy()
            self.state_manager.sync_state_to_view()
            
            log.debug(f"[FILE_CONFIG_MANAGER] Updated recent folders, now {len(self.recent_source_folders)} folders")

        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error updating recent folders: {e}")
    
    def get_recent_folders(self) -> List[str]:
        """Get the list of recent source folders."""
        return self.recent_source_folders.copy()
    
    def clear_recent_folders(self):
        """Clear the recent folders list."""
        try:
            self.recent_source_folders.clear()
            ud_config.set_value('recent_source_folders', [])
            self.state.recent_source_folders = []
            self.state_manager.sync_state_to_view()
            log.debug("[FILE_CONFIG_MANAGER] Cleared recent folders")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error clearing recent folders: {e}")

    # =============================================================================
    # SAVE LOCATION METHODS
    # =============================================================================
    
    def handle_save_option_change(self, save_option):
        """Handle save location option change."""
        try:
            log.debug(f"[FILE_CONFIG_MANAGER] Save option changed: {save_option}")
            
            if save_option == SaveOptions.SAME_AS_SOURCE:
                self._set_same_as_source()
            elif save_option == SaveOptions.SELECT_LOCATION:
                self._select_save_location()
            else:
                log.warning(f"[FILE_CONFIG_MANAGER] Unknown save option: {save_option}")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling save option change: {e}")

    def _set_same_as_source(self):
        """Set save location to same as source."""
        try:
            if self.selected_source and self.selected_source.get("path"):
                save_path = self.selected_source["path"]
                self.state.save_location = save_path
                self.state_manager.sync_state_to_view()
                log.debug(f"[FILE_CONFIG_MANAGER] Set save location to source: {save_path}")
            else:
                log.warning("[FILE_CONFIG_MANAGER] No source selected for 'same as source'")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error setting same as source: {e}")

    def _select_save_location(self):
        """Handle custom save location selection."""
        try:
            last_save_dir = ud_config.get_value(ud_keys.Paths.LAST_SAVE_DIR, "")
            
            # Use view's folder dialog for save location (simple BS method as requested)
            save_path = self.view.show_folder_dialog() if hasattr(self.view, 'show_folder_dialog') else None
            
            if save_path:
                ud_config.set_value(ud_keys.Paths.LAST_SAVE_DIR, save_path)
                self.state.save_location = save_path
                self.state_manager.sync_state_to_view()
                log.debug(f"[FILE_CONFIG_MANAGER] Selected save location: {save_path}")
            else:
                log.debug("[FILE_CONFIG_MANAGER] Save location selection cancelled")
                
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error selecting save location: {e}")

    # =============================================================================
    # UTILITY METHODS
    # =============================================================================
    
    def subscribe_add_files_channel(self):
        """Subscribe to add files events."""
        try:
            self.local_bus.subscribe(ViewEvents.ADD_FILES_REQUESTED.value, self._on_add_files_requested)
            log.debug("[FILE_CONFIG_MANAGER] Subscribed to ADD_FILES_REQUESTED events")
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error subscribing to add files events: {e}")

    def _on_add_files_requested(self, event_data):
        """Handle add files request."""
        try:
            log.debug("[FILE_CONFIG_MANAGER] Add files requested")
            self._select_files()
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error handling add files request: {e}")

    def _emit_error_dialog(self, title: str, message: str):
        """Emit error dialog request."""
        try:
            from ..ui_events import DialogRequestEvent
            self.local_bus.emit(
                ViewEvents.ERROR_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="error",
                    title=title,
                    extra_data={"message": message}
                )
            )
        except Exception as e:
            log.error(f"[FILE_CONFIG_MANAGER] Error emitting error dialog: {e}")
