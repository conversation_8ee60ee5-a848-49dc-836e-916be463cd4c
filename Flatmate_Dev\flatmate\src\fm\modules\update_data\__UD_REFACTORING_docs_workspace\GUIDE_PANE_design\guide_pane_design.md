# Guide Pane: Unified Contextual Guidance Widget

## Overview
The Guide Pane is a **state-driven contextual guidance widget** that unifies all user-facing guidance, help, and interactive options for the Update Data module. It replaces the fragmented approach of separate welcome panes and static instruction labels.

## Architecture Decision
**Problem:** Fragmented guidance across multiple widgets created inconsistent UX and maintenance overhead.
**Solution:** Single, state-driven widget that adapts its content and options based on user context and system state.

## Design Principles

### 1. State Machine Architecture
```
User Action → State Transition → Content Update → Option Display
```

**States:**
- `initial` - Welcome with steps overview
- `folder_selected` - File count and location
- `ready` - Process preparation with options
- `processing` - Live progress with cancel option
- `success` - Summary with next actions
- `error` - Error details with recovery options

### 2. Content Strategy
**Rich Formatting:**
- HTML for structured content (lists, emphasis, links)
- Dynamic variables for contextual information
- Consistent tone and styling across states

**Message Templates:**
```python
{
    'folder_selected': "Found {count} CSV files in {folder_name}",
    'ready': "Ready to process {count} files to {save_location}",
    'processing': "Processing {current} of {total} files...",
    'success': "Successfully processed {count} files to {save_location}",
    'error': "Error processing {filename}: {error_message}"
}
```

### 3. Interactive Options
**Dynamic Options:**
- **Buttons**: Primary actions (Process, Cancel, View Results)
- **Checkboxes**: Configuration toggles (Monitor folder, Overwrite existing)
- **State-based**: Options appear/disappear based on context

**Option Definition:**
```python
{
    'action': 'process_files',
    'text': 'Process Files',
    'enabled': lambda context: context.get('file_count', 0) > 0,
    'visible': lambda state: state in ['ready', 'folder_selected']
}
```

### 4. Event System
**Clean Decoupling:**
- `message_changed` signal for content updates
- `option_selected` signal for action triggers
- `config_changed` signal for checkbox toggles

**Event Payload:**
```python
{
    'event_type': 'option_selected',
    'action': 'view_results',
    'context': {current_state_data}
}
```

## Implementation Details

### Widget Structure
```
GuidePaneWidget (QFrame)
├── message_display (QTextEdit) - Rich content
├── options_container (QGroupBox) - Interactive elements
│   ├── action_buttons (QPushButton[])
│   └── config_checkboxes (QCheckBox[])
└── state_manager (internal)
```

### State Management
```python
class GuideState:
    state: str
    message: str
    options: List[Option]
    styling: Dict[str, str]
    
    def transition(self, new_state: str, context: Dict):
        self.state = new_state
        self.message = self.format_message(new_state, context)
        self.options = self.generate_options(new_state, context)
        self.styling = self.get_styling(new_state)
```

### Styling Guidelines
- **Visual Hierarchy**: Color coding for states (info=blue, warning=orange, error=red)
- **Responsive Layout**: Adapts to content length and option count
- **Consistent Spacing**: 12px margins, 8px spacing between elements
- **Typography**: 9pt Arial for readability, bold for emphasis

## Usage Patterns

### Basic State Transition
```python
guide_pane = GuidePaneWidget()
guide_pane.set_state('folder_selected', {
    'count': 5,
    'folder_name': '/path/to/data'
})
```

### Dynamic Options
```python
guide_pane.show_options([
    {
        'text': 'Process Files',
        'action': 'process_files',
        'primary': True
    },
    {
        'text': 'View Results',
        'action': 'view_results',
        'enabled': False  # Disabled until processing complete
    }
])
```

### Configuration Toggle
```python
guide_pane.add_config_option(
    key='monitor_folder',
    label='Monitor this folder for new files',
    default=False,
    description='Automatically detect new files in selected folder'
)
```

## Testing Strategy

### Unit Tests
- State transition logic
- Message formatting with variables
- Option visibility/enabled states
- Event emission and payload structure

### Integration Tests
- End-to-end user journeys
- Signal handling between components
- Styling consistency across states

### Visual Regression Tests
- Screenshot comparison for each state
- Responsive behavior at different sizes
- Accessibility compliance (screen readers)

## Performance Considerations
- **Lazy Loading**: Options widgets created on-demand
- **State Caching**: Pre-compute common state combinations
- **Minimal Re-renders**: Only update changed elements
- **Memory Management**: Proper cleanup of dynamic elements

## Future Extensibility
- **Plugin System**: Allow custom states and options
- **Localization**: Message templates with i18n support
- **Theming**: CSS-based styling system
- **Analytics**: Track user interaction patterns

## Migration Path
1. Replace welcome pane with initial state
2. Consolidate scattered instruction labels
3. Update event handlers to use new signal system
4. Remove deprecated guidance components
5. Update user flow documentation

This design ensures the Guide Pane becomes the single source of truth for all user guidance while maintaining flexibility for future enhancements.
