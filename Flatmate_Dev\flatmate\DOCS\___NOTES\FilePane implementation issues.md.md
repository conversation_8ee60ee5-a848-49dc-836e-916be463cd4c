# FilePane implementation issues.md
# user test notes:
Thursday, August 7, 2025 @ 10:50:16 PM


issue: Column: 'Name' is not stretching to fill available space. It should also read "Selected Files"
## the the parnet folder un this test reads:
[icon] ~/Downloads

## the containg folder reads 
[icon] ~/Doenloads\_flatma... (truncated by column)
- issue: path seperator mismatch on windows 
- issue: the file names are heavily truncated by the column width especially on smaller machines (tablet size )

-issue: The columns are not resizable

Issue: The column order should be 

`Selected Files`, `File Info`, `Size`, `Created`
It is currently 
`Name`, `File Info`, `Size`, `created`, `Type`

- The `Selected Files` column should stretch to fill available space.

The File Info curretly reads eg kiwibank | basic 

It should read e.g. `Kiwibank Basic CSV` (with no pipes)

currently `.csv` is in the `Type` Column

We should lose the `type` collumn as it is redundant.

Instead opting for the statement handler: `bank, variant, file type`  approach.

It may be neccesary to examine what the current file info object is actually returnng 