#!/usr/bin/env python3
"""
Visual test for GuidePaneWidget - allows manual inspection of the widget.

Run this to see how the guide pane looks and behaves.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parents[5]  # Go up to flatmate directory
sys.path.insert(0, str(project_root / "src"))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt

from fm.modules.update_data._view.center_panel_components.guide_pane import GuidePaneWidget


class GuidePaneTestWindow(QMainWindow):
    """Test window for visual inspection of GuidePaneWidget."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Guide Pane Visual Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        
        # Create guide pane
        self.guide_pane = GuidePaneWidget()
        layout.addWidget(self.guide_pane)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        # State test buttons
        btn_initial = QPushButton("Initial State")
        btn_initial.clicked.connect(lambda: self.guide_pane.reset_to_initial())
        controls_layout.addWidget(btn_initial)
        
        btn_folder = QPushButton("Folder Selected")
        btn_folder.clicked.connect(lambda: self.guide_pane.set_state('folder_selected', {'count': 5}))
        controls_layout.addWidget(btn_folder)
        
        btn_ready = QPushButton("Ready State")
        btn_ready.clicked.connect(lambda: self.guide_pane.set_state('ready', {'count': 5}))
        controls_layout.addWidget(btn_ready)
        
        btn_processing = QPushButton("Processing")
        btn_processing.clicked.connect(lambda: self.guide_pane.set_state('processing', {'current': 2, 'total': 5}))
        controls_layout.addWidget(btn_processing)
        
        btn_success = QPushButton("Success")
        btn_success.clicked.connect(lambda: self.guide_pane.show_success_summary(5))
        controls_layout.addWidget(btn_success)
        
        btn_error = QPushButton("Error")
        btn_error.clicked.connect(lambda: self.guide_pane.show_error_details('test.csv', 'Invalid format'))
        controls_layout.addWidget(btn_error)
        
        layout.addLayout(controls_layout)
        
        # Connect to guide pane signals
        self.guide_pane.message_changed.connect(self.on_message_changed)
        
        # Start with initial state
        self.guide_pane.reset_to_initial()
    
    def on_message_changed(self, message):
        """Handle message change signals."""
        print(f"Guide pane message changed: {message}")


def main():
    """Run the visual test."""
    app = QApplication(sys.argv)
    
    # Set dark theme for better visibility
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 5px 10px;
            border-radius: 3px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
    """)
    
    window = GuidePaneTestWindow()
    window.show()
    
    print("Guide Pane Visual Test")
    print("======================")
    print("Use the buttons to test different states:")
    print("- Initial State: Welcome message")
    print("- Folder Selected: Shows file count")
    print("- Ready State: Shows options")
    print("- Processing: Shows progress")
    print("- Success: Shows completion with options")
    print("- Error: Shows error message")
    print("\nWatch the console for signal emissions.")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
