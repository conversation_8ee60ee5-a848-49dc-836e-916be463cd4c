"""
Update Data view implementation.
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QMessageBox

from fm.core.services.event_bus import global_event_bus
from fm.core.services.logger import log
from fm.modules.base.base_module_view import BaseModuleView
from ._view.center_panel_layout import CenterPanelManager
from ._view.left_panel_layout import LeftPanelManager
from ..services.local_event_bus import (
    ViewEvents,
    update_data_local_bus,
)
from .interface import IUpdateDataView


class UpdateDataView(BaseModuleView):
    """
    Update Data view.

    Implements IUpdateDataView interface methods for presenter interaction.
    """
    cancel_clicked = Signal()
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)

    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        self.event_bus = global_event_bus
        self.local_bus = update_data_local_bus
        super().__init__(parent, gui_config, gui_keys)

    def setup_ui(self):
        self.left_panel_manager = LeftPanelManager()
        self.center_display = CenterPanelManager()
        self._create_guide_pane()
        self._subscribe_to_events()
        self._connect_signals()
        self._connect_center_pane_intents()
        self._connect_center_pane_intents()

    def setup_left_panel(self, layout):
        layout.addWidget(self.left_panel_manager)

    def setup_center_panel(self, layout):
        layout.addWidget(self.center_display)

    def _subscribe_to_events(self):
        # View renders from state events and file list updates
        self.local_bus.subscribe(ViewEvents.UI_STATE_CHANGED.value, self.update_ui_state)
        self.local_bus.subscribe(ViewEvents.STATUS_MESSAGE_CHANGED.value, self.update_status_message)
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.update_files_display)
        # Single canonical path: Managers -> FILE_LIST_UPDATED -> View
        self.local_bus.subscribe(ViewEvents.FILE_LIST_UPDATED.value, self.update_files_display)

        # Phase 3: subscribe to processing lifecycle and dialog requests
        self.local_bus.subscribe(ViewEvents.PROCESSING_STARTED.value, self._on_processing_started)
        self.local_bus.subscribe(ViewEvents.PROCESSING_COMPLETED.value, self._on_processing_completed)
        self.local_bus.subscribe(ViewEvents.ERROR_DIALOG_REQUESTED.value, self._on_dialog_requested)
        self.local_bus.subscribe(ViewEvents.SUCCESS_DIALOG_REQUESTED.value, self._on_dialog_requested)
    
    def _connect_signals(self):
        # Translate LeftPanel Qt signals into typed user-intent events on Local Event Bus
        # Use enumerated option types where applicable to ensure consistent payloads
        from ..models.config import SourceOptions
        def _normalize_source_select(opt_text: str) -> str:
            # Ensure we send a plain string matching our FileManager expectations
            # Prefer enum values when matching, otherwise pass-through the text
            if opt_text == SourceOptions.SELECT_FILES.value:
                return SourceOptions.SELECT_FILES
            if opt_text == SourceOptions.SELECT_FOLDER.value:
                return SourceOptions.SELECT_FOLDER
            return opt_text

        # IMPORTANT: Avoid duplicate dialog opens by ensuring only ONE source of SOURCE_SELECT_REQUESTED.
        # Separate channels to prevent collisions:
        # - Left Panel source-select (folder/files) -> SOURCE_SELECT_REQUESTED
        # - Center pane "Add files" -> FILES_ADD_REQUESTED (new, distinct), FileManager will map to SELECT_FILES
        self.left_panel_manager.source_select_requested.connect(
            lambda opt: self.local_bus.emit(
                ViewEvents.SOURCE_SELECT_REQUESTED.value,
                _normalize_source_select(opt)
            )
        )
        self.left_panel_manager.save_select_requested.connect(
            lambda: self.local_bus.emit(ViewEvents.DESTINATION_SELECT_REQUESTED.value, None)
        )
        self.left_panel_manager.process_clicked.connect(
            lambda: self.local_bus.emit(ViewEvents.PROCESS_REQUESTED.value, None)
        )
        self.left_panel_manager.cancel_clicked.connect(
            lambda: self.local_bus.emit(ViewEvents.CANCEL_REQUESTED.value, None)
        )
        self.left_panel_manager.source_option_changed.connect(
            lambda opt: self.local_bus.emit(ViewEvents.SOURCE_OPTION_CHANGED.value, opt)
        )
        self.left_panel_manager.save_option_changed.connect(
            lambda opt: self.local_bus.emit(ViewEvents.SAVE_OPTION_CHANGED.value, opt)
        )
        self.left_panel_manager.update_database_changed.connect(
            lambda checked: self.local_bus.emit(ViewEvents.UPDATE_DATABASE_CHANGED.value, checked)
        )

    def update_ui_state(self, ui_state_data):
        if 'can_process' in ui_state_data:
            self.set_process_enabled(ui_state_data['can_process'])
        if 'archive_enabled' in ui_state_data:
            self.set_archive_enabled(ui_state_data['archive_enabled'])
        if 'process_button_text' in ui_state_data:
            self.set_process_button_text(ui_state_data['process_button_text'])
        if 'processing' in ui_state_data:
            self.set_all_controls_enabled(not ui_state_data['processing'])

    def update_status_message(self, message_data):
        if hasattr(self, 'guide_pane') and 'message' in message_data:
            self.guide_pane.display(message_data['message'])

    def update_files_display(self, files_data):
        log.debug(f"[UD_VIEW] Received file display update: {type(files_data)}")
        # Accept dataclass FileListUpdatedEvent or FileDisplayUpdateEvent or dict payloads
        # Direct-widget paradigm: route straight to UDFileView (no layout-manager middleman)
        try:
            # Resolve file pane widget once
            file_pane = None
            if hasattr(self, 'center_display') and hasattr(self.center_display, 'file_pane'):
                file_pane = self.center_display.file_pane

            if hasattr(files_data, 'files'):
                files = getattr(files_data, 'files', []) or []
                source_path = getattr(files_data, 'source_path', "") or ""

                # Handle both FileInfoData objects and plain strings for backward compatibility
                if files and hasattr(files[0], 'path'):
                    # New format: FileInfoData objects - pass enriched data directly to file pane
                    log.debug(f"[UD_VIEW] Received {len(files)} enriched FileInfoData objects")
                    if file_pane:
                        file_pane.set_enriched_files(files)  # Pass enriched objects directly
                else:
                    # Legacy format: plain file paths
                    file_paths = files
                    log.debug(f"[UD_VIEW] Received {len(files)} plain file paths")
                    if file_pane:
                        file_pane.set_files(file_paths)
                # Optionally display source path hint without filtering
                if source_path and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_path)
            elif isinstance(files_data, dict) and 'files' in files_data:
                files = files_data.get('files', []) or []
                source_path = files_data.get('source_path', "") or ""

                # Handle both FileInfoData objects and plain strings for backward compatibility
                if files and hasattr(files[0], 'path'):
                    # New format: FileInfoData objects - pass enriched data directly to file pane
                    log.debug(f"[UD_VIEW] Received {len(files)} enriched FileInfoData objects (dict format)")
                    if file_pane:
                        file_pane.set_enriched_files(files)  # Pass enriched objects directly
                else:
                    # Legacy format: plain file paths
                    file_paths = files
                    log.debug(f"[UD_VIEW] Received {len(files)} plain file paths (dict format)")
                    if file_pane:
                        file_pane.set_files(file_paths)
                if source_path and hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_path)
            else:
                log.warning(f"[UD_VIEW] Unknown files_data format: {files_data}")
        except Exception as e:
            log.error(f"[UD_VIEW] Error updating files display: {e}")

    def set_process_enabled(self, enabled: bool):
        self.left_panel_manager.set_process_button_enabled(enabled)

    def set_archive_enabled(self, enabled: bool):
        self.set_save_select_enabled(enabled)

    def set_process_button_text(self, text: str):
        self.left_panel_manager.set_process_button_text(text)

    def set_all_controls_enabled(self, enabled: bool):
        self.left_panel_manager.set_process_button_enabled(enabled)

    def set_save_path(self, path: str):
        self.center_display.set_save_path(path)

    def get_save_option(self) -> str:
        """Get current save option selection."""
        return self.left_panel_manager.get_archive_option()

    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        return self.left_panel_manager.get_update_database()

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save selection controls."""
        self.left_panel_manager.set_archive_enabled(enabled)

    def set_source_option(self, option: str):
        """Set source option selection."""
        self.left_panel_manager.set_source_option(option)

    def display_selected_source(self, source_info: dict):
        if not source_info:
            return
        try:
            # Direct-widget routing
            file_pane = None
            if hasattr(self, 'center_display') and hasattr(self.center_display, 'file_pane'):
                file_pane = self.center_display.file_pane

            if source_info.get("type") == "folder":
                folder_path = source_info.get("path", "") or ""
                files = source_info.get("file_paths", []) or []
                if hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(folder_path)
                if file_pane:
                    file_pane.set_files(files)
            else:
                files = source_info.get("file_paths", []) or []
                source_dir = os.path.dirname(files[0]) if files else ''
                if hasattr(self.center_display, 'set_source_path'):
                    self.center_display.set_source_path(source_dir)
                if file_pane:
                    file_pane.set_files(files)
        except Exception as e:
            log.error(f"[UD_VIEW] Error displaying selected source: {e}")

    def display_enriched_file_info(self, file_info_list: List[Dict[str, Any]]) -> None:
        if not file_info_list:
            return
        log.debug(f"Displaying enriched file info: {file_info_list}")
        self.center_display.display_enriched_file_info(file_info_list)

    def show_error(self, message: str, title: str = "Error"):
        """
        Deprecated: dialogs must be requested via local bus dialog-request events.
        This method now emits ERROR_DIALOG_REQUESTED for compatibility callers.
        """
        try:
            from .ui_events import DialogRequestEvent
            self.local_bus.emit(
                ViewEvents.ERROR_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="error",
                    title=title or "Error",
                    extra_data={"message": message or "An error occurred."}
                )
            )
        except Exception as e:
            # Last resort: fall back to direct dialog if event emission fails
            QMessageBox.critical(self, title, message)

    def show_success(self, message: str, title: str = "Success"):
        """
        Deprecated: dialogs must be requested via local bus dialog-request events.
        This method now emits SUCCESS_DIALOG_REQUESTED for compatibility callers.
        """
        try:
            from .ui_events import DialogRequestEvent
            self.local_bus.emit(
                ViewEvents.SUCCESS_DIALOG_REQUESTED.value,
                DialogRequestEvent(
                    dialog_type="success",
                    title=title or "Success",
                    extra_data={"message": message or "Operation completed."}
                )
            )
        except Exception as e:
            # Last resort fallback
            QMessageBox.information(self, title, message)

    def show_folder_dialog(self, title: str, initial_dir: Optional[str] = None) -> str:
        """Deprecated shim. Emit FOLDER_DIALOG_REQUESTED and return empty string."""
        try:
            self.local_bus.emit(ViewEvents.FOLDER_DIALOG_REQUESTED.value, {
                "title": title or "Select Folder",
                "initial_dir": initial_dir or str(Path.home())
            })
        finally:
            # Dialog is owned by FileManager; callers should not expect a return
            return ""

    def show_files_dialog(self, title: str, initial_dir: Optional[str] = None, filter_str: str = "Data Files (*.csv *.ofx *.pdf)") -> List[str]:
        """Deprecated shim. Emit FILES_DIALOG_REQUESTED and return empty list."""
        try:
            self.local_bus.emit(ViewEvents.FILES_DIALOG_REQUESTED.value, {
                "title": title or "Select Files",
                "initial_dir": initial_dir or str(Path.home()),
                "filter": filter_str
            })
        finally:
            # Dialog is owned by FileManager; callers should not expect a return
            return []

    def _create_guide_pane(self):
        from ._view.center_panel_components.guide_pane import GuidePaneWidget
        self.guide_pane = GuidePaneWidget()

    def _connect_center_pane_intents(self):
        """
        Connect center pane (UDFileView) user intents to Local Event Bus.
        Dialogs are centralized in FileManager via Presenter subscriptions.
        """
        try:
            file_pane = None
            # Prefer v2 if available
            if hasattr(self.center_display, 'file_pane_v2') and self.center_display.file_pane_v2 is not None:
                file_pane = self.center_display.file_pane_v2
            elif hasattr(self.center_display, 'file_pane') and self.center_display.file_pane is not None:
                file_pane = self.center_display.file_pane

            # Wire "Add files" intent (if UDFileView exposes it)
            if file_pane is not None and hasattr(file_pane, 'add_files_requested'):
                # Use a distinct channel to avoid colliding with Left Panel source-select
                # Add a lightweight adapter in FileManager to treat FILES_ADD_REQUESTED as SELECT_FILES
                files_add_event_key = "add_files_requested"
                file_pane.add_files_requested.connect(
                    lambda: self.local_bus.emit(files_add_event_key, None)
                )
                log.debug("[UD_VIEW] Wired UDFileView.add_files_requested -> add_files_requested (distinct channel)")
        except Exception as e:
            log.error(f"[UD_VIEW] Error wiring center pane intents: {e}")

    def get_current_files(self) -> List[str]:
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                return self.center_display.file_pane_v2.get_files()
            elif hasattr(self.center_display, 'file_pane'):
                return self.center_display.file_pane.get_files()
            return []
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting current files: {e}")
            return []
    
    # === FILE OPERATIONS (New file_pane_v2 support) ===
    def add_files(self, files: List[str]) -> None:
        """Add files to the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                for file_path in files:
                    self.center_display.file_pane_v2.add_file(file_path)
                log.debug(f"[UD_VIEW] Added {len(files)} files to file_pane_v2")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for add_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error adding files: {e}")
    
    def remove_file(self, file_path: str) -> None:
        """Remove a file from the file view and publish canonical remove intent."""
        try:
            # Update local widget immediately for responsiveness
            if hasattr(self.center_display, 'file_pane_v2'):
                self.center_display.file_pane_v2.remove_file(file_path)
                log.debug(f"[UD_VIEW] Removed file locally: {file_path}")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for remove_file")

            # Publish canonical remove intent so FileListManager updates the canonical list
            from .ui_events import FileRemovedEvent  # local import to avoid cycles
            self.local_bus.emit(ViewEvents.FILE_REMOVED.value, FileRemovedEvent(file_path=file_path))
            log.debug("[UD_VIEW] Published FILE_REMOVED intent")
        except Exception as e:
            log.error(f"[UD_VIEW] Error removing file: {e}")
    
    def set_files(self, files: List[str]) -> None:
        """Deprecated path: prefer FILE_LIST_UPDATED -> update_files_display. Kept for compatibility."""
        try:
            if hasattr(self.center_display, 'file_pane'):
                self.center_display.file_pane.set_files(files)
                log.debug(f"[UD_VIEW] (compat) Set {len(files)} files in file_pane")
            else:
                log.warning("[UD_VIEW] file_pane not available for set_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error setting files: {e}")
    
    def get_selected_file(self) -> str:
        """Get the currently selected file path."""
        try:
            if hasattr(self.center_display, 'file_pane'):
                selected = self.center_display.file_pane.get_selected_file()
                return selected if selected else ""
            return ""
        except Exception as e:
            log.error(f"[UD_VIEW] Error getting selected file: {e}")
            return ""
    
    def clear_files(self) -> None:
        """Clear all files from the file view."""
        try:
            if hasattr(self.center_display, 'file_pane_v2'):
                self.center_display.file_pane_v2.clear_files()
                log.debug("[UD_VIEW] Cleared all files from file_pane_v2")
            else:
                log.warning("[UD_VIEW] file_pane_v2 not available for clear_files")
        except Exception as e:
            log.error(f"[UD_VIEW] Error clearing files: {e}")

    # === Phase 3: Processing lifecycle & dialog handlers ===
    def _on_processing_started(self, event_data):
        """
        Handle processing started:
        - event_data: ProcessingStartedEvent dataclass or dict-compatible
        - Disable inputs and update UI message
        """
        try:
            log.debug(f"[UD_VIEW] Processing started event received: {type(event_data)}")
            # Disable all interactions during processing
            self.set_all_controls_enabled(False)
            # Update status/guide
            file_count = 0
            job_sheet = None
            if hasattr(event_data, 'job_sheet'):
                job_sheet = event_data.job_sheet
            elif isinstance(event_data, dict):
                job_sheet = event_data.get('job_sheet')
            if isinstance(job_sheet, dict):
                file_count = len(job_sheet.get('filepaths', []) or [])
            self.update_status_message({'message': f"Processing started... {file_count} files"})
        except Exception as e:
            log.error(f"[UD_VIEW] Error handling processing started: {e}")

    def _on_processing_completed(self, event_data):
        """
        Handle processing completed:
        - event_data: ProcessingCompletedEvent dataclass or dict-compatible
        - Re-enable inputs and update status
        """
        try:
            log.debug(f"[UD_VIEW] Processing completed event received: {type(event_data)}")
            # Re-enable interactions
            self.set_all_controls_enabled(True)

            # Extract message
            message = ""
            success = False
            result = None
            if isinstance(event_data, dict):
                success = bool(event_data.get('success', False))
                result = event_data.get('result') or {}
            else:
                success = bool(getattr(event_data, 'success', False))
                result = getattr(event_data, 'result', {}) or {}

            if isinstance(result, dict):
                message = result.get('message') or result.get('error') or ""

            status = "Success" if success else "Error"
            composed = f"Processing completed: {status}. {message}".strip()
            self.update_status_message({'message': composed})
        except Exception as e:
            log.error(f"[UD_VIEW] Error handling processing completed: {e}")

    def _on_dialog_requested(self, event_data):
        """
        Handle dialog requests for success/error uniformly:
        - event_data: DialogRequestEvent dataclass or dict with dialog_type, title, extra_data.message
        """
        try:
            log.debug(f"[UD_VIEW] Dialog request event received: {type(event_data)}")
            dialog_type = None
            title = ""
            message = ""
            extra = {}

            if isinstance(event_data, dict):
                dialog_type = event_data.get('dialog_type')
                title = event_data.get('title') or ""
                extra = event_data.get('extra_data') or {}
            else:
                dialog_type = getattr(event_data, 'dialog_type', None)
                title = getattr(event_data, 'title', "") or ""
                extra = getattr(event_data, 'extra_data', {}) or {}

            if isinstance(extra, dict):
                message = extra.get('message', "") or message

            if dialog_type == "error":
                self.show_error(message or "An error occurred.", title=title or "Error")
            elif dialog_type == "success":
                self.show_success(message or "Operation completed.", title=title or "Success")
            else:
                log.warning(f"[UD_VIEW] Unknown dialog type: {dialog_type}")
        except Exception as e:
            log.error(f"[UD_VIEW] Error handling dialog request: {e}")

